<Window x:Class="NetworkMonitor.UI.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Settings - Network Monitor" 
        Height="500" 
        Width="600"
        MinHeight="400"
        MinWidth="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Icon="../icon.ico">
    
    <Window.Resources>
        <Style TargetType="GroupBox">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="10"/>
        </Style>
        <Style TargetType="CheckBox">
            <Setter Property="Margin" Value="5"/>
        </Style>
        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="3"/>
        </Style>
        <Style TargetType="ComboBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="3"/>
        </Style>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Monitoring Settings -->
                <GroupBox Header="Monitoring Settings">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Label Grid.Row="0" Grid.Column="0" Content="Update Interval (seconds):"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" Name="UpdateIntervalComboBox" Width="150" HorizontalAlignment="Left">
                            <ComboBoxItem Content="1" Tag="1000"/>
                            <ComboBoxItem Content="2" Tag="2000"/>
                            <ComboBoxItem Content="5" Tag="5000" IsSelected="True"/>
                            <ComboBoxItem Content="10" Tag="10000"/>
                            <ComboBoxItem Content="30" Tag="30000"/>
                        </ComboBox>

                        <Label Grid.Row="1" Grid.Column="0" Content="Data Retention (days):"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Name="DataRetentionTextBox" Width="150" HorizontalAlignment="Left" Text="30"/>

                        <CheckBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Name="StartWithWindowsCheckBox" Content="Start with Windows"/>
                        <CheckBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Name="MinimizeToTrayCheckBox" Content="Minimize to system tray" IsChecked="True"/>
                    </Grid>
                </GroupBox>

                <!-- Alert Settings -->
                <GroupBox Header="Alert Settings">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <CheckBox Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" Name="EnableAlertsCheckBox" Content="Enable Alerts" IsChecked="True"/>
                        <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Name="SuspiciousConnectionAlertsCheckBox" Content="Alert on suspicious connections" IsChecked="True"/>
                        <CheckBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Name="HighBandwidthAlertsCheckBox" Content="Alert on high bandwidth usage" IsChecked="True"/>

                        <Label Grid.Row="3" Grid.Column="0" Content="High bandwidth threshold (MB/s):"/>
                        <TextBox Grid.Row="3" Grid.Column="1" Name="BandwidthThresholdTextBox" Width="150" HorizontalAlignment="Left" Text="10"/>

                        <Label Grid.Row="4" Grid.Column="0" Content="Alert timeout (seconds):"/>
                        <TextBox Grid.Row="4" Grid.Column="1" Name="AlertTimeoutTextBox" Width="150" HorizontalAlignment="Left" Text="5"/>
                    </Grid>
                </GroupBox>

                <!-- Display Settings -->
                <GroupBox Header="Display Settings">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <CheckBox Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" Name="AlwaysOnTopCheckBox" Content="Always on top"/>
                        <CheckBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Name="ShowNotificationsCheckBox" Content="Show notifications" IsChecked="True"/>

                        <Label Grid.Row="2" Grid.Column="0" Content="Theme:"/>
                        <ComboBox Grid.Row="2" Grid.Column="1" Name="ThemeComboBox" Width="150" HorizontalAlignment="Left">
                            <ComboBoxItem Content="Light" IsSelected="True"/>
                            <ComboBoxItem Content="Dark"/>
                            <ComboBoxItem Content="System"/>
                        </ComboBox>
                    </Grid>
                </GroupBox>

                <!-- Security Settings -->
                <GroupBox Header="Security Settings">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <CheckBox Grid.Row="0" Name="MonitorSuspiciousConnectionsCheckBox" Content="Monitor suspicious connections" IsChecked="True"/>
                        <CheckBox Grid.Row="1" Name="LogSecurityEventsCheckBox" Content="Log security events" IsChecked="True"/>
                        <CheckBox Grid.Row="2" Name="BlockSuspiciousConnectionsCheckBox" Content="Block suspicious connections (requires admin)"/>
                    </Grid>
                </GroupBox>

                <!-- Logging Settings -->
                <GroupBox Header="Logging Settings">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <CheckBox Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" Name="EnableLoggingCheckBox" Content="Enable logging" IsChecked="True"/>

                        <Label Grid.Row="1" Grid.Column="0" Content="Log level:"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" Name="LogLevelComboBox" Width="150" HorizontalAlignment="Left">
                            <ComboBoxItem Content="Debug"/>
                            <ComboBoxItem Content="Info" IsSelected="True"/>
                            <ComboBoxItem Content="Warning"/>
                            <ComboBoxItem Content="Error"/>
                        </ComboBox>

                        <Label Grid.Row="2" Grid.Column="0" Content="Log retention (days):"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Name="LogRetentionTextBox" Width="150" HorizontalAlignment="Left" Text="30"/>

                        <Button Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Name="OpenLogFolderButton" Content="Open Log Folder" Click="OpenLogFolder_Click" HorizontalAlignment="Left"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="10">
            <Button Name="OkButton" Content="OK" Click="Ok_Click" IsDefault="True"/>
            <Button Name="CancelButton" Content="Cancel" Click="Cancel_Click" IsCancel="True"/>
            <Button Name="ApplyButton" Content="Apply" Click="Apply_Click"/>
            <Button Name="ResetButton" Content="Reset to Defaults" Click="Reset_Click"/>
        </StackPanel>
    </Grid>
</Window>
