# Network Monitor

A comprehensive Windows network monitoring application built with WPF and C#.

## Features

### ✅ Completed Features

#### Core Monitoring
- **Real-time Network Connection Monitoring**: Monitor TCP and UDP connections with process information
- **Bandwidth Monitoring**: Track download/upload speeds and data usage per network interface
- **Application Usage Tracking**: Monitor network usage per application with detailed statistics
- **System Tray Integration**: Minimize to system tray with notifications and quick access

#### Data Management
- **Data Export**: Export monitoring data to CSV or JSON formats
- **Data Persistence**: Automatic saving and loading of historical data
- **Configurable Data Retention**: Set how long to keep historical data

#### User Interface
- **Multi-tab Interface**: Organized tabs for Dashboard, Connections, Bandwidth, and Applications
- **Real-time Charts**: Interactive bandwidth charts using LiveCharts
- **Filtering and Sorting**: Filter connections by protocol, state, and suspicious activity
- **Responsive Design**: Resizable interface with proper layout management

#### Settings and Configuration
- **Comprehensive Settings Window**: Configure all aspects of the application
- **Monitoring Settings**: Update intervals, data retention, startup options
- **Alert Settings**: Configure notifications for suspicious connections and high bandwidth usage
- **Display Settings**: Theme selection, always-on-top, notification preferences
- **Security Settings**: Configure security monitoring and logging options
- **Settings Import/Export**: Backup and restore application settings

#### Logging and Diagnostics
- **File-based Logging**: Comprehensive logging with configurable levels (Debug, Info, Warning, Error, Critical)
- **Automatic Log Rotation**: Automatic cleanup of old log files
- **Error Handling**: Robust error handling with user-friendly error messages
- **Performance Monitoring**: Track application performance and resource usage

#### Security Features
- **Suspicious Connection Detection**: Identify potentially malicious network connections
- **Security Event Logging**: Log security-related events for analysis
- **Administrator Privilege Detection**: Warn users about required permissions

## System Requirements

- Windows 7 or later
- .NET Framework 4.7.2 or later
- Administrator privileges (recommended for full functionality)

## Installation

1. Download the latest release from the releases page
2. Extract the files to a folder of your choice
3. Run `NetworkMonitor.exe`
4. For full functionality, run as administrator

## Usage

### Starting Monitoring
1. Launch the application
2. Click "Start Monitoring" in the toolbar
3. Select your network interface from the dropdown
4. Monitor real-time data in the various tabs

### Exporting Data
1. Go to File → Export Data
2. Choose CSV or JSON format
3. Select save location
4. Data includes connections, bandwidth history, and application usage

### Configuring Settings
1. Go to Tools → Settings
2. Configure monitoring intervals, alerts, and display options
3. Click "Apply" or "OK" to save changes
4. Some changes may require a restart

### Viewing Charts
- **Dashboard Tab**: Quick overview with real-time bandwidth chart
- **Bandwidth Tab**: Detailed bandwidth history with interactive charts
- Use mouse to hover over chart points for detailed information

## Architecture

### Core Components
- **ConnectionMonitor**: Monitors network connections using Windows API
- **BandwidthMonitor**: Tracks bandwidth usage using performance counters
- **ProcessMapper**: Maps network connections to running processes
- **NotificationService**: Handles system tray and notifications

### Services
- **LoggingService**: Handles application logging with file rotation
- **SettingsService**: Manages application configuration and persistence
- **DataStorage**: Handles data persistence and export functionality

### Models
- **NetworkConnection**: Represents a network connection with process information
- **BandwidthData**: Represents bandwidth measurements over time
- **ApplicationUsage**: Tracks network usage per application

## Development

### Building from Source
1. Clone the repository
2. Open `NetworkMonitor.sln` in Visual Studio
3. Restore NuGet packages
4. Build the solution

### Dependencies
- **LiveCharts.Wpf**: For real-time charts and data visualization
- **Newtonsoft.Json**: For JSON serialization and settings management
- **System.Management**: For WMI queries and system information

### Testing
Run the unit tests using:
```
dotnet test Tests/NetworkMonitor.Tests.csproj
```

## Configuration Files

### Settings Location
Settings are stored in: `%APPDATA%\NetworkMonitor\settings.json`

### Log Files Location
Log files are stored in: `%APPDATA%\NetworkMonitor\Logs\`

### Data Files Location
Historical data is stored in: `%APPDATA%\NetworkMonitor\Data\`

## Troubleshooting

### Common Issues

**"Access Denied" errors**
- Run the application as administrator
- Check Windows Firewall settings

**No network interfaces detected**
- Ensure network adapters are enabled
- Check Windows services (Performance Logs and Alerts)

**Charts not displaying**
- Ensure monitoring is started
- Check that data is being collected in other tabs

**High CPU usage**
- Increase update interval in settings
- Reduce data retention period

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- LiveCharts for excellent charting capabilities
- Windows API documentation and community examples
- .NET and WPF framework teams
