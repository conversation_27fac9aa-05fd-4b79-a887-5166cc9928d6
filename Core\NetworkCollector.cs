using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using NetworkMonitor.Models;
using NetworkMonitor.Utils;

namespace NetworkMonitor.Core
{
    public class NetworkCollector : INotifyPropertyChanged, IDisposable
    {
        private readonly ConnectionMonitor _connectionMonitor;
        private readonly BandwidthMonitor _bandwidthMonitor;
        private readonly ProcessMapper _processMapper;
        private readonly DataStorage _dataStorage;
        private readonly Timer _saveTimer;
        private bool _isRunning;
        private bool _disposed;

        public event EventHandler<NetworkConnection> SuspiciousConnectionDetected;
        public event EventHandler<ApplicationUsage> HighBandwidthUsageDetected;
        public event EventHandler<string> AlertGenerated;

        public ConnectionMonitor ConnectionMonitor => _connectionMonitor;
        public BandwidthMonitor BandwidthMonitor => _bandwidthMonitor;
        public ProcessMapper ProcessMapper => _processMapper;
        public DataStorage DataStorage => _dataStorage;

        public bool IsRunning
        {
            get => _isRunning;
            private set
            {
                _isRunning = value;
                OnPropertyChanged(nameof(IsRunning));
            }
        }

        // Configuration properties
        public double HighBandwidthThreshold { get; set; } = 10 * 1024 * 1024; // 10 MB/s
        public bool EnableSuspiciousConnectionDetection { get; set; } = true;
        public bool EnableHighBandwidthAlerts { get; set; } = true;
        public bool AutoSaveData { get; set; } = true;
        public TimeSpan AutoSaveInterval { get; set; } = TimeSpan.FromMinutes(5);
        public TimeSpan DataRetentionPeriod { get; set; } = TimeSpan.FromDays(7);

        public NetworkCollector()
        {
            _connectionMonitor = new ConnectionMonitor();
            _bandwidthMonitor = new BandwidthMonitor();
            _processMapper = new ProcessMapper();
            _dataStorage = new DataStorage();

            // Setup auto-save timer
            _saveTimer = new Timer(AutoSaveData_Callback, null, Timeout.Infinite, Timeout.Infinite);

            SetupEventHandlers();
            LoadSavedData();
        }

        private void SetupEventHandlers()
        {
            _connectionMonitor.ConnectionAdded += OnConnectionAdded;
            _connectionMonitor.ConnectionUpdated += OnConnectionUpdated;
            _bandwidthMonitor.PropertyChanged += OnBandwidthMonitorPropertyChanged;
        }

        private void OnConnectionAdded(object sender, NetworkConnection connection)
        {
            // Update process mapper
            _processMapper.UpdateApplicationUsage(connection);

            // Check for suspicious connections
            if (EnableSuspiciousConnectionDetection && connection.IsSuspicious)
            {
                SuspiciousConnectionDetected?.Invoke(this, connection);
                AlertGenerated?.Invoke(this, 
                    $"Suspicious connection detected: {connection.ProcessName} -> {connection.RemoteEndpoint}");
            }

            // Check for suspicious processes
            if (EnableSuspiciousConnectionDetection && _processMapper.IsSuspiciousProcess(connection.ProcessId))
            {
                AlertGenerated?.Invoke(this,
                    $"Connection from suspicious process: {connection.ProcessName} (PID: {connection.ProcessId})");
            }
        }

        private void OnConnectionUpdated(object sender, NetworkConnection connection)
        {
            _processMapper.UpdateApplicationUsage(connection);
        }

        private void OnBandwidthMonitorPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(BandwidthMonitor.CurrentBandwidth) && EnableHighBandwidthAlerts)
            {
                CheckHighBandwidthUsage();
            }
        }

        private void CheckHighBandwidthUsage()
        {
            var highUsageApps = _processMapper.GetApplicationsWithHighBandwidth(HighBandwidthThreshold);
            
            foreach (var app in highUsageApps)
            {
                HighBandwidthUsageDetected?.Invoke(this, app);
                AlertGenerated?.Invoke(this,
                    $"High bandwidth usage: {app.ProcessName} using {app.CurrentDownloadSpeedFormatted} down / {app.CurrentUploadSpeedFormatted} up");
            }
        }

        public void Start()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(NetworkCollector));

            try
            {
                _connectionMonitor.Start();
                _bandwidthMonitor.Start();

                if (AutoSaveData)
                {
                    _saveTimer.Change(AutoSaveInterval, AutoSaveInterval);
                }

                IsRunning = true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to start network collector: {ex.Message}", ex);
            }
        }

        public void Stop()
        {
            try
            {
                _connectionMonitor.Stop();
                _bandwidthMonitor.Stop();
                _saveTimer.Change(Timeout.Infinite, Timeout.Infinite);

                // Save data before stopping
                if (AutoSaveData)
                {
                    SaveAllData();
                }

                IsRunning = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error stopping network collector: {ex.Message}");
            }
        }

        private void LoadSavedData()
        {
            try
            {
                // Load bandwidth history
                var bandwidthHistory = _dataStorage.LoadBandwidthHistory();
                foreach (var data in bandwidthHistory.TakeLast(1000)) // Limit to last 1000 entries
                {
                    _bandwidthMonitor.BandwidthHistory.Add(data);
                }

                // Load application usage
                var applications = _dataStorage.LoadApplicationUsage();
                foreach (var app in applications)
                {
                    _processMapper.GetApplicationUsage(app.ProcessId);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading saved data: {ex.Message}");
            }
        }

        private void AutoSaveData_Callback(object state)
        {
            if (!IsRunning || _disposed) return;

            try
            {
                SaveAllData();
                CleanupOldData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during auto-save: {ex.Message}");
            }
        }

        public void SaveAllData()
        {
            try
            {
                // Save current connections
                _dataStorage.SaveConnections(_connectionMonitor.Connections);

                // Save bandwidth history
                _dataStorage.SaveBandwidthHistory(_bandwidthMonitor.BandwidthHistory);

                // Save application usage
                var applications = _processMapper.GetTopApplicationsByUsage(1000); // Save top 1000
                _dataStorage.SaveApplicationUsage(applications);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving data: {ex.Message}");
            }
        }

        private void CleanupOldData()
        {
            try
            {
                _dataStorage.CleanupOldData(DataRetentionPeriod);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error cleaning up old data: {ex.Message}");
            }
        }

        public void ExportData(string filePath, ExportFormat format, ExportType type)
        {
            try
            {
                switch (format)
                {
                    case ExportFormat.CSV:
                        ExportToCsv(filePath, type);
                        break;
                    case ExportFormat.JSON:
                        ExportToJson(filePath, type);
                        break;
                    default:
                        throw new ArgumentException($"Unsupported export format: {format}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error exporting data: {ex.Message}", ex);
            }
        }

        private void ExportToCsv(string filePath, ExportType type)
        {
            switch (type)
            {
                case ExportType.Connections:
                    _dataStorage.ExportToCsv(filePath, _connectionMonitor.Connections);
                    break;
                case ExportType.Bandwidth:
                    _dataStorage.ExportBandwidthToCsv(filePath, _bandwidthMonitor.BandwidthHistory);
                    break;
                case ExportType.Applications:
                    _dataStorage.ExportApplicationsToCsv(filePath, _processMapper.GetTopApplicationsByUsage(1000));
                    break;
                default:
                    throw new ArgumentException($"Unsupported export type: {type}");
            }
        }

        private void ExportToJson(string filePath, ExportType type)
        {
            switch (type)
            {
                case ExportType.Connections:
                    var connectionsJson = Newtonsoft.Json.JsonConvert.SerializeObject(_connectionMonitor.Connections, Newtonsoft.Json.Formatting.Indented);
                    System.IO.File.WriteAllText(filePath, connectionsJson);
                    break;
                case ExportType.Bandwidth:
                    var bandwidthJson = Newtonsoft.Json.JsonConvert.SerializeObject(_bandwidthMonitor.BandwidthHistory, Newtonsoft.Json.Formatting.Indented);
                    System.IO.File.WriteAllText(filePath, bandwidthJson);
                    break;
                case ExportType.Applications:
                    var applicationsJson = Newtonsoft.Json.JsonConvert.SerializeObject(_processMapper.GetTopApplicationsByUsage(1000), Newtonsoft.Json.Formatting.Indented);
                    System.IO.File.WriteAllText(filePath, applicationsJson);
                    break;
                default:
                    throw new ArgumentException($"Unsupported export type: {type}");
            }
        }

        public NetworkStatistics GetStatistics()
        {
            var connections = _connectionMonitor.Connections;
            var bandwidth = _bandwidthMonitor.CurrentBandwidth;
            var processStats = _processMapper.GetProcessStatistics();

            return new NetworkStatistics
            {
                TotalConnections = connections.Count,
                EstablishedConnections = connections.Count(c => c.State == "Established"),
                ListeningConnections = connections.Count(c => c.State == "Listening"),
                SuspiciousConnections = connections.Count(c => c.IsSuspicious),
                CurrentDownloadSpeed = bandwidth?.DownloadSpeed ?? 0,
                CurrentUploadSpeed = bandwidth?.UploadSpeed ?? 0,
                TotalDataTransferred = bandwidth?.TotalDownloaded + bandwidth?.TotalUploaded ?? 0,
                ActiveApplications = processStats.GetValueOrDefault("ProcessesWithConnections", 0),
                MonitoringDuration = IsRunning ? DateTime.Now - (_connectionMonitor.Connections.FirstOrDefault()?.FirstSeen ?? DateTime.Now) : TimeSpan.Zero
            };
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                Stop();
                _saveTimer?.Dispose();
                _connectionMonitor?.Dispose();
                _bandwidthMonitor?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing network collector: {ex.Message}");
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    public enum ExportFormat
    {
        CSV,
        JSON
    }

    public enum ExportType
    {
        Connections,
        Bandwidth,
        Applications
    }

    public class NetworkStatistics
    {
        public int TotalConnections { get; set; }
        public int EstablishedConnections { get; set; }
        public int ListeningConnections { get; set; }
        public int SuspiciousConnections { get; set; }
        public double CurrentDownloadSpeed { get; set; }
        public double CurrentUploadSpeed { get; set; }
        public long TotalDataTransferred { get; set; }
        public int ActiveApplications { get; set; }
        public TimeSpan MonitoringDuration { get; set; }
    }
}
