using System;
using System.ComponentModel;
using System.Net;

namespace NetworkMonitor.Models
{
    public class NetworkConnection : INotifyPropertyChanged
    {
        private string _state;
        private long _bytesReceived;
        private long _bytesSent;

        public int ProcessId { get; set; }
        public string ProcessName { get; set; }
        public string ProcessPath { get; set; }
        public string Protocol { get; set; }
        public IPAddress LocalAddress { get; set; }
        public int LocalPort { get; set; }
        public IPAddress RemoteAddress { get; set; }
        public int RemotePort { get; set; }
        
        public string State
        {
            get => _state;
            set
            {
                _state = value;
                OnPropertyChanged(nameof(State));
            }
        }

        public long BytesReceived
        {
            get => _bytesReceived;
            set
            {
                _bytesReceived = value;
                OnPropertyChanged(nameof(BytesReceived));
                OnPropertyChanged(nameof(BytesReceivedFormatted));
            }
        }

        public long BytesSent
        {
            get => _bytesSent;
            set
            {
                _bytesSent = value;
                OnPropertyChanged(nameof(BytesSent));
                OnPropertyChanged(nameof(BytesSentFormatted));
            }
        }

        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }

        // Formatted properties for display
        public string LocalEndpoint => $"{LocalAddress}:{LocalPort}";
        public string RemoteEndpoint => $"{RemoteAddress}:{RemotePort}";
        
        public string BytesReceivedFormatted => FormatBytes(BytesReceived);
        public string BytesSentFormatted => FormatBytes(BytesSent);
        public string TotalBytesFormatted => FormatBytes(BytesReceived + BytesSent);

        public string Duration
        {
            get
            {
                var duration = LastSeen - FirstSeen;
                if (duration.TotalDays >= 1)
                    return $"{duration.Days}d {duration.Hours}h {duration.Minutes}m";
                if (duration.TotalHours >= 1)
                    return $"{duration.Hours}h {duration.Minutes}m {duration.Seconds}s";
                if (duration.TotalMinutes >= 1)
                    return $"{duration.Minutes}m {duration.Seconds}s";
                return $"{duration.Seconds}s";
            }
        }

        public bool IsSuspicious
        {
            get
            {
                // Basic heuristics for suspicious connections
                if (RemotePort == 22 || RemotePort == 23 || RemotePort == 3389) // SSH, Telnet, RDP
                    return true;
                if (RemoteAddress != null && IsPrivateIP(RemoteAddress))
                    return false;
                if (BytesSent > 1024 * 1024 * 100) // More than 100MB sent
                    return true;
                return false;
            }
        }

        private bool IsPrivateIP(IPAddress address)
        {
            var bytes = address.GetAddressBytes();
            if (bytes.Length != 4) return false;

            // 10.0.0.0/8
            if (bytes[0] == 10) return true;
            
            // **********/12
            if (bytes[0] == 172 && bytes[1] >= 16 && bytes[1] <= 31) return true;
            
            // ***********/16
            if (bytes[0] == 192 && bytes[1] == 168) return true;
            
            // *********/8 (localhost)
            if (bytes[0] == 127) return true;

            return false;
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public override bool Equals(object obj)
        {
            if (obj is NetworkConnection other)
            {
                return ProcessId == other.ProcessId &&
                       Protocol == other.Protocol &&
                       LocalAddress?.Equals(other.LocalAddress) == true &&
                       LocalPort == other.LocalPort &&
                       RemoteAddress?.Equals(other.RemoteAddress) == true &&
                       RemotePort == other.RemotePort;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(ProcessId, Protocol, LocalAddress, LocalPort, RemoteAddress, RemotePort);
        }
    }
}
