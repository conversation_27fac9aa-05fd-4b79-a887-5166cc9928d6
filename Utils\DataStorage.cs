using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using NetworkMonitor.Models;

namespace NetworkMonitor.Utils
{
    public class DataStorage
    {
        private readonly string _dataDirectory;
        private readonly string _connectionsFile;
        private readonly string _bandwidthFile;
        private readonly string _applicationsFile;

        public DataStorage()
        {
            _dataDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "NetworkMonitor");
            
            _connectionsFile = Path.Combine(_dataDirectory, "connections.json");
            _bandwidthFile = Path.Combine(_dataDirectory, "bandwidth.json");
            _applicationsFile = Path.Combine(_dataDirectory, "applications.json");

            EnsureDirectoryExists();
        }

        private void EnsureDirectoryExists()
        {
            try
            {
                if (!Directory.Exists(_dataDirectory))
                {
                    Directory.CreateDirectory(_dataDirectory);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating data directory: {ex.Message}");
            }
        }

        public void SaveConnections(IEnumerable<NetworkConnection> connections)
        {
            try
            {
                var json = JsonConvert.SerializeObject(connections, Formatting.Indented);
                File.WriteAllText(_connectionsFile, json, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving connections: {ex.Message}");
            }
        }

        public List<NetworkConnection> LoadConnections()
        {
            try
            {
                if (File.Exists(_connectionsFile))
                {
                    var json = File.ReadAllText(_connectionsFile, Encoding.UTF8);
                    return JsonConvert.DeserializeObject<List<NetworkConnection>>(json) ?? new List<NetworkConnection>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading connections: {ex.Message}");
            }
            
            return new List<NetworkConnection>();
        }

        public void SaveBandwidthHistory(IEnumerable<BandwidthData> bandwidthData)
        {
            try
            {
                var json = JsonConvert.SerializeObject(bandwidthData, Formatting.Indented);
                File.WriteAllText(_bandwidthFile, json, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving bandwidth data: {ex.Message}");
            }
        }

        public List<BandwidthData> LoadBandwidthHistory()
        {
            try
            {
                if (File.Exists(_bandwidthFile))
                {
                    var json = File.ReadAllText(_bandwidthFile, Encoding.UTF8);
                    return JsonConvert.DeserializeObject<List<BandwidthData>>(json) ?? new List<BandwidthData>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading bandwidth data: {ex.Message}");
            }
            
            return new List<BandwidthData>();
        }

        public void SaveApplicationUsage(IEnumerable<ApplicationUsage> applications)
        {
            try
            {
                var json = JsonConvert.SerializeObject(applications, Formatting.Indented);
                File.WriteAllText(_applicationsFile, json, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving application data: {ex.Message}");
            }
        }

        public List<ApplicationUsage> LoadApplicationUsage()
        {
            try
            {
                if (File.Exists(_applicationsFile))
                {
                    var json = File.ReadAllText(_applicationsFile, Encoding.UTF8);
                    return JsonConvert.DeserializeObject<List<ApplicationUsage>>(json) ?? new List<ApplicationUsage>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading application data: {ex.Message}");
            }
            
            return new List<ApplicationUsage>();
        }

        public void ExportToCsv(string filePath, IEnumerable<NetworkConnection> connections)
        {
            try
            {
                var csv = new StringBuilder();
                csv.AppendLine("ProcessName,ProcessId,Protocol,LocalAddress,LocalPort,RemoteAddress,RemotePort,State,BytesReceived,BytesSent,FirstSeen,LastSeen");

                foreach (var connection in connections)
                {
                    csv.AppendLine($"{EscapeCsv(connection.ProcessName)},{connection.ProcessId},{connection.Protocol}," +
                                  $"{connection.LocalAddress},{connection.LocalPort},{connection.RemoteAddress},{connection.RemotePort}," +
                                  $"{EscapeCsv(connection.State)},{connection.BytesReceived},{connection.BytesSent}," +
                                  $"{connection.FirstSeen:yyyy-MM-dd HH:mm:ss},{connection.LastSeen:yyyy-MM-dd HH:mm:ss}");
                }

                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error exporting connections to CSV: {ex.Message}", ex);
            }
        }

        public void ExportBandwidthToCsv(string filePath, IEnumerable<BandwidthData> bandwidthData)
        {
            try
            {
                var csv = new StringBuilder();
                csv.AppendLine("Timestamp,InterfaceName,DownloadSpeed,UploadSpeed,TotalDownloaded,TotalUploaded");

                foreach (var data in bandwidthData)
                {
                    csv.AppendLine($"{data.Timestamp:yyyy-MM-dd HH:mm:ss},{EscapeCsv(data.InterfaceName)}," +
                                  $"{data.DownloadSpeed},{data.UploadSpeed},{data.TotalDownloaded},{data.TotalUploaded}");
                }

                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error exporting bandwidth data to CSV: {ex.Message}", ex);
            }
        }

        public void ExportApplicationsToCsv(string filePath, IEnumerable<ApplicationUsage> applications)
        {
            try
            {
                var csv = new StringBuilder();
                csv.AppendLine("ProcessName,ProcessId,ProcessPath,BytesReceived,BytesSent,FirstSeen,LastSeen");

                foreach (var app in applications)
                {
                    csv.AppendLine($"{EscapeCsv(app.ProcessName)},{app.ProcessId},{EscapeCsv(app.ProcessPath)}," +
                                  $"{app.BytesReceived},{app.BytesSent}," +
                                  $"{app.FirstSeen:yyyy-MM-dd HH:mm:ss},{app.LastSeen:yyyy-MM-dd HH:mm:ss}");
                }

                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error exporting applications to CSV: {ex.Message}", ex);
            }
        }

        private string EscapeCsv(string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            if (value.Contains(",") || value.Contains("\"") || value.Contains("\n") || value.Contains("\r"))
            {
                return "\"" + value.Replace("\"", "\"\"") + "\"";
            }

            return value;
        }

        public void CleanupOldData(TimeSpan maxAge)
        {
            try
            {
                var cutoffDate = DateTime.Now - maxAge;

                // Clean up bandwidth history
                var bandwidthData = LoadBandwidthHistory();
                var filteredBandwidth = bandwidthData.Where(b => b.Timestamp >= cutoffDate).ToList();
                if (filteredBandwidth.Count != bandwidthData.Count)
                {
                    SaveBandwidthHistory(filteredBandwidth);
                }

                // Clean up application usage (keep applications but reset old data)
                var applications = LoadApplicationUsage();
                var filteredApplications = applications.Where(a => a.LastSeen >= cutoffDate).ToList();
                if (filteredApplications.Count != applications.Count)
                {
                    SaveApplicationUsage(filteredApplications);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error cleaning up old data: {ex.Message}");
            }
        }

        public long GetDataDirectorySize()
        {
            try
            {
                if (!Directory.Exists(_dataDirectory))
                    return 0;

                var files = Directory.GetFiles(_dataDirectory, "*", SearchOption.AllDirectories);
                return files.Sum(file => new FileInfo(file).Length);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating data directory size: {ex.Message}");
                return 0;
            }
        }

        public void ClearAllData()
        {
            try
            {
                if (File.Exists(_connectionsFile))
                    File.Delete(_connectionsFile);
                
                if (File.Exists(_bandwidthFile))
                    File.Delete(_bandwidthFile);
                
                if (File.Exists(_applicationsFile))
                    File.Delete(_applicationsFile);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing data: {ex.Message}");
            }
        }
    }
}
