using System;
using System.Diagnostics;
using System.Windows;
using NetworkMonitor.Services;

namespace NetworkMonitor.UI
{
    public partial class SettingsWindow : Window
    {
        private readonly SettingsService _settingsService;
        private bool _hasChanges = false;

        public SettingsWindow()
        {
            InitializeComponent();
            _settingsService = new SettingsService();
            LoadSettings();
        }

        private void LoadSettings()
        {
            try
            {
                var settings = _settingsService.LoadSettings();

                // Monitoring Settings
                SetComboBoxValue(UpdateIntervalComboBox, settings.UpdateInterval.ToString());
                DataRetentionTextBox.Text = settings.DataRetentionDays.ToString();
                StartWithWindowsCheckBox.IsChecked = settings.StartWithWindows;
                MinimizeToTrayCheckBox.IsChecked = settings.MinimizeToTray;

                // Alert Settings
                EnableAlertsCheckBox.IsChecked = settings.EnableAlerts;
                SuspiciousConnectionAlertsCheckBox.IsChecked = settings.SuspiciousConnectionAlerts;
                HighBandwidthAlertsCheckBox.IsChecked = settings.HighBandwidthAlerts;
                BandwidthThresholdTextBox.Text = settings.BandwidthThresholdMBps.ToString();
                AlertTimeoutTextBox.Text = settings.AlertTimeoutSeconds.ToString();

                // Display Settings
                AlwaysOnTopCheckBox.IsChecked = settings.AlwaysOnTop;
                ShowNotificationsCheckBox.IsChecked = settings.ShowNotifications;
                SetComboBoxValue(ThemeComboBox, settings.Theme);

                // Security Settings
                MonitorSuspiciousConnectionsCheckBox.IsChecked = settings.MonitorSuspiciousConnections;
                LogSecurityEventsCheckBox.IsChecked = settings.LogSecurityEvents;
                BlockSuspiciousConnectionsCheckBox.IsChecked = settings.BlockSuspiciousConnections;

                // Logging Settings
                EnableLoggingCheckBox.IsChecked = settings.EnableLogging;
                SetComboBoxValue(LogLevelComboBox, settings.LogLevel);
                LogRetentionTextBox.Text = settings.LogRetentionDays.ToString();

                _hasChanges = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading settings: {ex.Message}", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetComboBoxValue(System.Windows.Controls.ComboBox comboBox, string value)
        {
            foreach (System.Windows.Controls.ComboBoxItem item in comboBox.Items)
            {
                if (item.Content.ToString() == value || item.Tag?.ToString() == value)
                {
                    comboBox.SelectedItem = item;
                    break;
                }
            }
        }

        private AppSettings GetCurrentSettings()
        {
            return new AppSettings
            {
                // Monitoring Settings
                UpdateInterval = int.Parse(((System.Windows.Controls.ComboBoxItem)UpdateIntervalComboBox.SelectedItem).Tag.ToString()),
                DataRetentionDays = int.Parse(DataRetentionTextBox.Text),
                StartWithWindows = StartWithWindowsCheckBox.IsChecked ?? false,
                MinimizeToTray = MinimizeToTrayCheckBox.IsChecked ?? true,

                // Alert Settings
                EnableAlerts = EnableAlertsCheckBox.IsChecked ?? true,
                SuspiciousConnectionAlerts = SuspiciousConnectionAlertsCheckBox.IsChecked ?? true,
                HighBandwidthAlerts = HighBandwidthAlertsCheckBox.IsChecked ?? true,
                BandwidthThresholdMBps = double.Parse(BandwidthThresholdTextBox.Text),
                AlertTimeoutSeconds = int.Parse(AlertTimeoutTextBox.Text),

                // Display Settings
                AlwaysOnTop = AlwaysOnTopCheckBox.IsChecked ?? false,
                ShowNotifications = ShowNotificationsCheckBox.IsChecked ?? true,
                Theme = ((System.Windows.Controls.ComboBoxItem)ThemeComboBox.SelectedItem).Content.ToString(),

                // Security Settings
                MonitorSuspiciousConnections = MonitorSuspiciousConnectionsCheckBox.IsChecked ?? true,
                LogSecurityEvents = LogSecurityEventsCheckBox.IsChecked ?? true,
                BlockSuspiciousConnections = BlockSuspiciousConnectionsCheckBox.IsChecked ?? false,

                // Logging Settings
                EnableLogging = EnableLoggingCheckBox.IsChecked ?? true,
                LogLevel = ((System.Windows.Controls.ComboBoxItem)LogLevelComboBox.SelectedItem).Content.ToString(),
                LogRetentionDays = int.Parse(LogRetentionTextBox.Text)
            };
        }

        private void Ok_Click(object sender, RoutedEventArgs e)
        {
            if (ApplySettings())
            {
                DialogResult = true;
                Close();
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            ApplySettings();
        }

        private bool ApplySettings()
        {
            try
            {
                var settings = GetCurrentSettings();
                _settingsService.SaveSettings(settings);
                _hasChanges = false;

                MessageBox.Show("Settings saved successfully. Some changes may require a restart to take effect.",
                    "Settings Saved", MessageBoxButton.OK, MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving settings: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private void Reset_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to reset all settings to their default values?",
                "Reset Settings", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _settingsService.ResetToDefaults();
                LoadSettings();
                MessageBox.Show("Settings have been reset to default values.",
                    "Settings Reset", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void OpenLogFolder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (Application.Current is App app && app.LoggingService != null)
                {
                    var logDirectory = app.LoggingService.GetLogDirectory();
                    Process.Start("explorer.exe", logDirectory);
                }
                else
                {
                    MessageBox.Show("Unable to access log directory.", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening log folder: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (_hasChanges)
            {
                var result = MessageBox.Show("You have unsaved changes. Do you want to save them before closing?",
                    "Unsaved Changes", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    if (!ApplySettings())
                    {
                        e.Cancel = true;
                        return;
                    }
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnClosing(e);
        }

        // Track changes
        private void OnSettingChanged(object sender, RoutedEventArgs e)
        {
            _hasChanges = true;
        }

        private void OnTextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            _hasChanges = true;
        }

        private void OnSelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            _hasChanges = true;
        }
    }
}
