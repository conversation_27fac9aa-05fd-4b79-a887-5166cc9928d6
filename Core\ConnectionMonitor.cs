using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using NetworkMonitor.Models;
using NetworkMonitor.Utils;

namespace NetworkMonitor.Core
{
    public class ConnectionMonitor : INotifyPropertyChanged, IDisposable
    {
        private readonly Timer _updateTimer;
        private readonly object _lockObject = new object();
        private readonly Dictionary<string, NetworkConnection> _connectionCache = new Dictionary<string, NetworkConnection>();
        private bool _isRunning;
        private bool _disposed;

        public ObservableCollection<NetworkConnection> Connections { get; }
        public event EventHandler<NetworkConnection> ConnectionAdded;
        public event EventHandler<NetworkConnection> ConnectionRemoved;
        public event EventHandler<NetworkConnection> ConnectionUpdated;

        public bool IsRunning
        {
            get => _isRunning;
            private set
            {
                _isRunning = value;
                OnPropertyChanged(nameof(IsRunning));
            }
        }

        public int UpdateInterval { get; set; } = 2000; // 2 seconds

        public ConnectionMonitor()
        {
            Connections = new ObservableCollection<NetworkConnection>();
            _updateTimer = new Timer(UpdateConnections, null, Timeout.Infinite, Timeout.Infinite);
        }

        public void Start()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ConnectionMonitor));
            
            IsRunning = true;
            _updateTimer.Change(0, UpdateInterval);
        }

        public void Stop()
        {
            IsRunning = false;
            _updateTimer.Change(Timeout.Infinite, Timeout.Infinite);
        }

        private async void UpdateConnections(object state)
        {
            if (!IsRunning || _disposed) return;

            try
            {
                await Task.Run(() =>
                {
                    var currentConnections = new Dictionary<string, NetworkConnection>();
                    
                    // Get TCP connections
                    GetTcpConnections(currentConnections);
                    
                    // Get UDP connections
                    GetUdpConnections(currentConnections);

                    // Update the collection on UI thread
                    System.Windows.Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        UpdateConnectionCollection(currentConnections);
                    }));
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating connections: {ex.Message}");
            }
        }

        private void GetTcpConnections(Dictionary<string, NetworkConnection> connections)
        {
            // Get IPv4 TCP connections
            GetTcpConnectionsInternal(AF_INET, connections);
            
            // Get IPv6 TCP connections
            GetTcpConnectionsInternal(AF_INET6, connections);
        }

        private void GetTcpConnectionsInternal(int ipVersion, Dictionary<string, NetworkConnection> connections)
        {
            int bufferSize = 0;
            uint result = NativeAPI.GetExtendedTcpTable(IntPtr.Zero, ref bufferSize, true, ipVersion, 
                NativeAPI.TCP_TABLE_CLASS.TCP_TABLE_OWNER_PID_ALL);

            if (result != NativeAPI.ERROR_INSUFFICIENT_BUFFER)
                return;

            IntPtr tcpTablePtr = Marshal.AllocHGlobal(bufferSize);
            try
            {
                result = NativeAPI.GetExtendedTcpTable(tcpTablePtr, ref bufferSize, true, ipVersion,
                    NativeAPI.TCP_TABLE_CLASS.TCP_TABLE_OWNER_PID_ALL);

                if (result != NativeAPI.NO_ERROR)
                    return;

                if (ipVersion == AF_INET)
                    ProcessTcpTable(tcpTablePtr, connections);
                else
                    ProcessTcp6Table(tcpTablePtr, connections);
            }
            finally
            {
                Marshal.FreeHGlobal(tcpTablePtr);
            }
        }

        private void ProcessTcpTable(IntPtr tablePtr, Dictionary<string, NetworkConnection> connections)
        {
            var table = Marshal.PtrToStructure<NativeAPI.MIB_TCPTABLE_OWNER_PID>(tablePtr);
            IntPtr rowPtr = IntPtr.Add(tablePtr, Marshal.SizeOf<uint>());

            for (int i = 0; i < table.dwNumEntries; i++)
            {
                var row = Marshal.PtrToStructure<NativeAPI.MIB_TCPROW_OWNER_PID>(rowPtr);
                
                var connection = CreateTcpConnection(row);
                if (connection != null)
                {
                    string key = GetConnectionKey(connection);
                    connections[key] = connection;
                }

                rowPtr = IntPtr.Add(rowPtr, Marshal.SizeOf<NativeAPI.MIB_TCPROW_OWNER_PID>());
            }
        }

        private void ProcessTcp6Table(IntPtr tablePtr, Dictionary<string, NetworkConnection> connections)
        {
            var table = Marshal.PtrToStructure<NativeAPI.MIB_TCP6TABLE_OWNER_PID>(tablePtr);
            IntPtr rowPtr = IntPtr.Add(tablePtr, Marshal.SizeOf<uint>());

            for (int i = 0; i < table.dwNumEntries; i++)
            {
                var row = Marshal.PtrToStructure<NativeAPI.MIB_TCP6ROW_OWNER_PID>(rowPtr);
                
                var connection = CreateTcp6Connection(row);
                if (connection != null)
                {
                    string key = GetConnectionKey(connection);
                    connections[key] = connection;
                }

                rowPtr = IntPtr.Add(rowPtr, Marshal.SizeOf<NativeAPI.MIB_TCP6ROW_OWNER_PID>());
            }
        }

        private NetworkConnection CreateTcpConnection(NativeAPI.MIB_TCPROW_OWNER_PID row)
        {
            try
            {
                var processInfo = GetProcessInfo((int)row.dwOwningPid);
                
                return new NetworkConnection
                {
                    ProcessId = (int)row.dwOwningPid,
                    ProcessName = processInfo.Name,
                    ProcessPath = processInfo.Path,
                    Protocol = "TCP",
                    LocalAddress = NativeAPI.UintToIPAddress(row.dwLocalAddr),
                    LocalPort = NativeAPI.NetworkToHostOrder((int)row.dwLocalPort),
                    RemoteAddress = NativeAPI.UintToIPAddress(row.dwRemoteAddr),
                    RemotePort = NativeAPI.NetworkToHostOrder((int)row.dwRemotePort),
                    State = NativeAPI.GetConnectionStateString(row.dwState),
                    FirstSeen = DateTime.Now,
                    LastSeen = DateTime.Now
                };
            }
            catch
            {
                return null;
            }
        }

        private NetworkConnection CreateTcp6Connection(NativeAPI.MIB_TCP6ROW_OWNER_PID row)
        {
            try
            {
                var processInfo = GetProcessInfo((int)row.dwOwningPid);
                
                return new NetworkConnection
                {
                    ProcessId = (int)row.dwOwningPid,
                    ProcessName = processInfo.Name,
                    ProcessPath = processInfo.Path,
                    Protocol = "TCP6",
                    LocalAddress = new IPAddress(row.ucLocalAddr),
                    LocalPort = NativeAPI.NetworkToHostOrder((int)row.dwLocalPort),
                    RemoteAddress = new IPAddress(row.ucRemoteAddr),
                    RemotePort = NativeAPI.NetworkToHostOrder((int)row.dwRemotePort),
                    State = NativeAPI.GetConnectionStateString(row.dwState),
                    FirstSeen = DateTime.Now,
                    LastSeen = DateTime.Now
                };
            }
            catch
            {
                return null;
            }
        }

        private void GetUdpConnections(Dictionary<string, NetworkConnection> connections)
        {
            // Get IPv4 UDP connections
            GetUdpConnectionsInternal(AF_INET, connections);
            
            // Get IPv6 UDP connections
            GetUdpConnectionsInternal(AF_INET6, connections);
        }

        private void GetUdpConnectionsInternal(int ipVersion, Dictionary<string, NetworkConnection> connections)
        {
            int bufferSize = 0;
            uint result = NativeAPI.GetExtendedUdpTable(IntPtr.Zero, ref bufferSize, true, ipVersion,
                NativeAPI.UDP_TABLE_CLASS.UDP_TABLE_OWNER_PID);

            if (result != NativeAPI.ERROR_INSUFFICIENT_BUFFER)
                return;

            IntPtr udpTablePtr = Marshal.AllocHGlobal(bufferSize);
            try
            {
                result = NativeAPI.GetExtendedUdpTable(udpTablePtr, ref bufferSize, true, ipVersion,
                    NativeAPI.UDP_TABLE_CLASS.UDP_TABLE_OWNER_PID);

                if (result != NativeAPI.NO_ERROR)
                    return;

                if (ipVersion == AF_INET)
                    ProcessUdpTable(udpTablePtr, connections);
                else
                    ProcessUdp6Table(udpTablePtr, connections);
            }
            finally
            {
                Marshal.FreeHGlobal(udpTablePtr);
            }
        }

        private void ProcessUdpTable(IntPtr tablePtr, Dictionary<string, NetworkConnection> connections)
        {
            var table = Marshal.PtrToStructure<NativeAPI.MIB_UDPTABLE_OWNER_PID>(tablePtr);
            IntPtr rowPtr = IntPtr.Add(tablePtr, Marshal.SizeOf<uint>());

            for (int i = 0; i < table.dwNumEntries; i++)
            {
                var row = Marshal.PtrToStructure<NativeAPI.MIB_UDPROW_OWNER_PID>(rowPtr);
                
                var connection = CreateUdpConnection(row);
                if (connection != null)
                {
                    string key = GetConnectionKey(connection);
                    connections[key] = connection;
                }

                rowPtr = IntPtr.Add(rowPtr, Marshal.SizeOf<NativeAPI.MIB_UDPROW_OWNER_PID>());
            }
        }

        private void ProcessUdp6Table(IntPtr tablePtr, Dictionary<string, NetworkConnection> connections)
        {
            var table = Marshal.PtrToStructure<NativeAPI.MIB_UDP6TABLE_OWNER_PID>(tablePtr);
            IntPtr rowPtr = IntPtr.Add(tablePtr, Marshal.SizeOf<uint>());

            for (int i = 0; i < table.dwNumEntries; i++)
            {
                var row = Marshal.PtrToStructure<NativeAPI.MIB_UDP6ROW_OWNER_PID>(rowPtr);
                
                var connection = CreateUdp6Connection(row);
                if (connection != null)
                {
                    string key = GetConnectionKey(connection);
                    connections[key] = connection;
                }

                rowPtr = IntPtr.Add(rowPtr, Marshal.SizeOf<NativeAPI.MIB_UDP6ROW_OWNER_PID>());
            }
        }

        private NetworkConnection CreateUdpConnection(NativeAPI.MIB_UDPROW_OWNER_PID row)
        {
            try
            {
                var processInfo = GetProcessInfo((int)row.dwOwningPid);
                
                return new NetworkConnection
                {
                    ProcessId = (int)row.dwOwningPid,
                    ProcessName = processInfo.Name,
                    ProcessPath = processInfo.Path,
                    Protocol = "UDP",
                    LocalAddress = NativeAPI.UintToIPAddress(row.dwLocalAddr),
                    LocalPort = NativeAPI.NetworkToHostOrder((int)row.dwLocalPort),
                    RemoteAddress = IPAddress.Any,
                    RemotePort = 0,
                    State = "Listening",
                    FirstSeen = DateTime.Now,
                    LastSeen = DateTime.Now
                };
            }
            catch
            {
                return null;
            }
        }

        private NetworkConnection CreateUdp6Connection(NativeAPI.MIB_UDP6ROW_OWNER_PID row)
        {
            try
            {
                var processInfo = GetProcessInfo((int)row.dwOwningPid);
                
                return new NetworkConnection
                {
                    ProcessId = (int)row.dwOwningPid,
                    ProcessName = processInfo.Name,
                    ProcessPath = processInfo.Path,
                    Protocol = "UDP6",
                    LocalAddress = new IPAddress(row.ucLocalAddr),
                    LocalPort = NativeAPI.NetworkToHostOrder((int)row.dwLocalPort),
                    RemoteAddress = IPAddress.IPv6Any,
                    RemotePort = 0,
                    State = "Listening",
                    FirstSeen = DateTime.Now,
                    LastSeen = DateTime.Now
                };
            }
            catch
            {
                return null;
            }
        }

        private (string Name, string Path) GetProcessInfo(int processId)
        {
            try
            {
                if (processId == 0)
                    return ("System Idle Process", "");
                if (processId == 4)
                    return ("System", "");

                using var process = Process.GetProcessById(processId);
                return (process.ProcessName, process.MainModule?.FileName ?? "");
            }
            catch
            {
                return ($"PID {processId}", "");
            }
        }

        private string GetConnectionKey(NetworkConnection connection)
        {
            return $"{connection.Protocol}:{connection.ProcessId}:{connection.LocalAddress}:{connection.LocalPort}:{connection.RemoteAddress}:{connection.RemotePort}";
        }

        private void UpdateConnectionCollection(Dictionary<string, NetworkConnection> currentConnections)
        {
            lock (_lockObject)
            {
                // Remove connections that no longer exist
                var toRemove = _connectionCache.Keys.Except(currentConnections.Keys).ToList();
                foreach (var key in toRemove)
                {
                    var connection = _connectionCache[key];
                    _connectionCache.Remove(key);
                    Connections.Remove(connection);
                    ConnectionRemoved?.Invoke(this, connection);
                }

                // Add new connections and update existing ones
                foreach (var kvp in currentConnections)
                {
                    if (_connectionCache.ContainsKey(kvp.Key))
                    {
                        // Update existing connection
                        var existing = _connectionCache[kvp.Key];
                        existing.LastSeen = DateTime.Now;
                        existing.State = kvp.Value.State;
                        ConnectionUpdated?.Invoke(this, existing);
                    }
                    else
                    {
                        // Add new connection
                        _connectionCache[kvp.Key] = kvp.Value;
                        Connections.Add(kvp.Value);
                        ConnectionAdded?.Invoke(this, kvp.Value);
                    }
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            if (_disposed) return;
            
            Stop();
            _updateTimer?.Dispose();
            _disposed = true;
        }

        private const int AF_INET = 2;
        private const int AF_INET6 = 23;
    }
}
