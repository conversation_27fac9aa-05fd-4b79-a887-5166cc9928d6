<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>Network Monitor</AssemblyTitle>
    <AssemblyDescription>Comprehensive Windows Network Monitoring Application</AssemblyDescription>
    <AssemblyCompany>NetworkMonitor</AssemblyCompany>
    <AssemblyProduct>Network Monitor</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © 2024</Copyright>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="7.0.2" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Configuration.Install" />
  </ItemGroup>

</Project>
