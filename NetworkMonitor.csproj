<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>Network Monitor</AssemblyTitle>
    <AssemblyDescription>Comprehensive Windows Network Monitoring Application</AssemblyDescription>
    <AssemblyCompany>NetworkMonitor</AssemblyCompany>
    <AssemblyProduct>Network Monitor</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © 2024</Copyright>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="7.0.2" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="7.0.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
  </ItemGroup>

</Project>
