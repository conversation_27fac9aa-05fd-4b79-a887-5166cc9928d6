<Window x:Class="NetworkMonitor.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        Title="Network Monitor"
        Height="800"
        Width="1200"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Icon="../icon.ico">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="5,0"/>
        </Style>

        <Style x:Key="MetricValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#007ACC"/>
        </Style>

        <Style x:Key="MetricLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0" Background="White" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1">
            <MenuItem Header="_File">
                <MenuItem Header="_Export Data..." Click="ExportData_Click"/>
                <Separator/>
                <MenuItem Header="E_xit" Click="Exit_Click"/>
            </MenuItem>
            <MenuItem Header="_View">
                <MenuItem Header="_Refresh" Click="Refresh_Click"/>
                <MenuItem Header="_Clear History" Click="ClearHistory_Click"/>
                <Separator/>
                <MenuItem Header="_Always on Top" IsCheckable="True" Click="AlwaysOnTop_Click"/>
            </MenuItem>
            <MenuItem Header="_Tools">
                <MenuItem Header="_Settings..." Click="Settings_Click"/>
                <MenuItem Header="_About..." Click="About_Click"/>
            </MenuItem>
        </Menu>

        <!-- Toolbar -->
        <ToolBar Grid.Row="1" Background="White" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1">
            <Button Name="StartStopButton" Click="StartStop_Click" Padding="10,5">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="StartStopText" Text="Start Monitoring"/>
                </StackPanel>
            </Button>
            <Separator/>
            <TextBlock Text="Update Interval:" VerticalAlignment="Center" Margin="5,0"/>
            <ComboBox Name="UpdateIntervalComboBox" Width="80" SelectedIndex="1" SelectionChanged="UpdateInterval_Changed">
                <ComboBoxItem Content="0.5s" Tag="500"/>
                <ComboBoxItem Content="1s" Tag="1000"/>
                <ComboBoxItem Content="2s" Tag="2000"/>
                <ComboBoxItem Content="5s" Tag="5000"/>
            </ComboBox>
            <Separator/>
            <TextBlock Text="Interface:" VerticalAlignment="Center" Margin="5,0"/>
            <ComboBox Name="InterfaceComboBox" Width="200" DisplayMemberPath="Description" SelectionChanged="Interface_Changed"/>
        </ToolBar>

        <!-- Main Content -->
        <TabControl Grid.Row="2" Name="MainTabControl" Margin="5">

            <!-- Dashboard Tab -->
            <TabItem Header="Dashboard">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Quick Stats -->
                    <Grid Grid.Row="0" Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <GroupBox Grid.Column="0" Header="Current Speed">
                            <StackPanel>
                                <TextBlock Text="Download" Style="{StaticResource MetricLabelStyle}"/>
                                <TextBlock Name="CurrentDownloadSpeed" Text="0 B/s" Style="{StaticResource MetricValueStyle}"/>
                                <TextBlock Text="Upload" Style="{StaticResource MetricLabelStyle}" Margin="0,10,0,0"/>
                                <TextBlock Name="CurrentUploadSpeed" Text="0 B/s" Style="{StaticResource MetricValueStyle}"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Grid.Column="1" Header="Active Connections">
                            <StackPanel>
                                <TextBlock Text="Total" Style="{StaticResource MetricLabelStyle}"/>
                                <TextBlock Name="TotalConnections" Text="0" Style="{StaticResource MetricValueStyle}"/>
                                <TextBlock Text="Established" Style="{StaticResource MetricLabelStyle}" Margin="0,10,0,0"/>
                                <TextBlock Name="EstablishedConnections" Text="0" Style="{StaticResource MetricValueStyle}"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Grid.Column="2" Header="Data Usage (Session)">
                            <StackPanel>
                                <TextBlock Text="Downloaded" Style="{StaticResource MetricLabelStyle}"/>
                                <TextBlock Name="SessionDownloaded" Text="0 B" Style="{StaticResource MetricValueStyle}"/>
                                <TextBlock Text="Uploaded" Style="{StaticResource MetricLabelStyle}" Margin="0,10,0,0"/>
                                <TextBlock Name="SessionUploaded" Text="0 B" Style="{StaticResource MetricValueStyle}"/>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Grid.Column="3" Header="Peak Speed (24h)">
                            <StackPanel>
                                <TextBlock Text="Download" Style="{StaticResource MetricLabelStyle}"/>
                                <TextBlock Name="PeakDownloadSpeed" Text="0 B/s" Style="{StaticResource MetricValueStyle}"/>
                                <TextBlock Text="Upload" Style="{StaticResource MetricLabelStyle}" Margin="0,10,0,0"/>
                                <TextBlock Name="PeakUploadSpeed" Text="0 B/s" Style="{StaticResource MetricValueStyle}"/>
                            </StackPanel>
                        </GroupBox>
                    </Grid>

                    <!-- Charts Area -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <GroupBox Grid.Column="0" Header="Bandwidth History">
                            <lvc:CartesianChart Name="BandwidthChart"
                                               LegendLocation="Bottom"
                                               Hoverable="False"
                                               DataTooltip="{x:Null}">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Title="Time">
                                        <lvc:Axis.Separator>
                                            <lvc:Separator StrokeThickness="1" StrokeDashArray="2"/>
                                        </lvc:Axis.Separator>
                                    </lvc:Axis>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis Title="Speed (MB/s)">
                                        <lvc:Axis.Separator>
                                            <lvc:Separator StrokeThickness="0.5" StrokeDashArray="4"/>
                                        </lvc:Axis.Separator>
                                    </lvc:Axis>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </GroupBox>

                        <GroupBox Grid.Column="1" Header="Top Applications">
                            <DataGrid Name="TopApplicationsGrid"
                                     AutoGenerateColumns="False"
                                     HeadersVisibility="Column">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Application" Binding="{Binding ProcessName}" Width="*"/>
                                    <DataGridTextColumn Header="Usage" Binding="{Binding TotalBytesFormatted}" Width="80"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </GroupBox>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- Connections Tab -->
            <TabItem Header="Connections">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Filters -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="Filter:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Name="ProtocolFilter" Width="80" SelectedIndex="0" SelectionChanged="Filter_Changed">
                            <ComboBoxItem Content="All"/>
                            <ComboBoxItem Content="TCP"/>
                            <ComboBoxItem Content="UDP"/>
                        </ComboBox>
                        <TextBlock Text="State:" VerticalAlignment="Center" Margin="20,0,10,0"/>
                        <ComboBox Name="StateFilter" Width="120" SelectedIndex="0" SelectionChanged="Filter_Changed">
                            <ComboBoxItem Content="All"/>
                            <ComboBoxItem Content="Established"/>
                            <ComboBoxItem Content="Listening"/>
                        </ComboBox>
                        <CheckBox Name="ShowSuspiciousOnly" Content="Suspicious Only"
                                 VerticalAlignment="Center" Margin="20,0,0,0"
                                 Checked="Filter_Changed" Unchecked="Filter_Changed"/>
                    </StackPanel>

                    <!-- Connections Grid -->
                    <DataGrid Grid.Row="1" Name="ConnectionsGrid"
                             AutoGenerateColumns="False"
                             CanUserSortColumns="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Process" Binding="{Binding ProcessName}" Width="120"/>
                            <DataGridTextColumn Header="PID" Binding="{Binding ProcessId}" Width="60"/>
                            <DataGridTextColumn Header="Protocol" Binding="{Binding Protocol}" Width="80"/>
                            <DataGridTextColumn Header="Local Address" Binding="{Binding LocalEndpoint}" Width="150"/>
                            <DataGridTextColumn Header="Remote Address" Binding="{Binding RemoteEndpoint}" Width="150"/>
                            <DataGridTextColumn Header="State" Binding="{Binding State}" Width="100"/>
                            <DataGridTextColumn Header="Received" Binding="{Binding BytesReceivedFormatted}" Width="100"/>
                            <DataGridTextColumn Header="Sent" Binding="{Binding BytesSentFormatted}" Width="100"/>
                            <DataGridTextColumn Header="Duration" Binding="{Binding Duration}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- Bandwidth Tab -->
            <TabItem Header="Bandwidth">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="2*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Current Stats -->
                    <GroupBox Grid.Row="0" Header="Current Bandwidth" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Download Speed" Style="{StaticResource MetricLabelStyle}"/>
                                <TextBlock Name="BandwidthDownloadSpeed" Text="0 B/s" Style="{StaticResource MetricValueStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Upload Speed" Style="{StaticResource MetricLabelStyle}"/>
                                <TextBlock Name="BandwidthUploadSpeed" Text="0 B/s" Style="{StaticResource MetricValueStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="Total Downloaded" Style="{StaticResource MetricLabelStyle}"/>
                                <TextBlock Name="BandwidthTotalDownloaded" Text="0 B" Style="{StaticResource MetricValueStyle}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3">
                                <TextBlock Text="Total Uploaded" Style="{StaticResource MetricLabelStyle}"/>
                                <TextBlock Name="BandwidthTotalUploaded" Text="0 B" Style="{StaticResource MetricValueStyle}"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <!-- Bandwidth Chart -->
                    <GroupBox Grid.Row="1" Header="Bandwidth History Chart" Margin="0,0,0,10">
                        <lvc:CartesianChart Name="DetailedBandwidthChart"
                                           LegendLocation="Right"
                                           Hoverable="True">
                            <lvc:CartesianChart.AxisX>
                                <lvc:Axis Title="Time">
                                    <lvc:Axis.Separator>
                                        <lvc:Separator StrokeThickness="1" StrokeDashArray="2"/>
                                    </lvc:Axis.Separator>
                                </lvc:Axis>
                            </lvc:CartesianChart.AxisX>
                            <lvc:CartesianChart.AxisY>
                                <lvc:Axis Title="Speed (MB/s)">
                                    <lvc:Axis.Separator>
                                        <lvc:Separator StrokeThickness="0.5" StrokeDashArray="4"/>
                                    </lvc:Axis.Separator>
                                </lvc:Axis>
                            </lvc:CartesianChart.AxisY>
                        </lvc:CartesianChart>
                    </GroupBox>

                    <!-- Bandwidth History Data -->
                    <GroupBox Grid.Row="2" Header="Recent History">
                        <DataGrid Name="BandwidthHistoryGrid"
                                 AutoGenerateColumns="False">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Time" Binding="{Binding Timestamp, StringFormat=HH:mm:ss}" Width="80"/>
                                <DataGridTextColumn Header="Download" Binding="{Binding DownloadSpeedFormatted}" Width="100"/>
                                <DataGridTextColumn Header="Upload" Binding="{Binding UploadSpeedFormatted}" Width="100"/>
                                <DataGridTextColumn Header="Total" Binding="{Binding TotalSpeedFormatted}" Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- Applications Tab -->
            <TabItem Header="Applications">
                <Grid Margin="10">
                    <DataGrid Name="ApplicationsGrid"
                             AutoGenerateColumns="False"
                             CanUserSortColumns="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Application" Binding="{Binding ProcessName}" Width="150"/>
                            <DataGridTextColumn Header="PID" Binding="{Binding ProcessId}" Width="60"/>
                            <DataGridTextColumn Header="Path" Binding="{Binding ProcessPath}" Width="300"/>
                            <DataGridTextColumn Header="Downloaded" Binding="{Binding BytesReceivedFormatted}" Width="100"/>
                            <DataGridTextColumn Header="Uploaded" Binding="{Binding BytesSentFormatted}" Width="100"/>
                            <DataGridTextColumn Header="Total" Binding="{Binding TotalBytesFormatted}" Width="100"/>
                            <DataGridTextColumn Header="Current Down" Binding="{Binding CurrentDownloadSpeedFormatted}" Width="100"/>
                            <DataGridTextColumn Header="Current Up" Binding="{Binding CurrentUploadSpeedFormatted}" Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3" Background="White" BorderBrush="#CCCCCC" BorderThickness="0,1,0,0">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="Ready" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="ConnectionCountStatus" Text="Connections: 0" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="MonitoringStatus" Text="Monitoring: Stopped" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Name="LastUpdateStatus" Text="Last Update: Never" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
