using System;
using System.Windows;
using System.Windows.Threading;
using NetworkMonitor.Services;

namespace NetworkMonitor
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private NotificationService _notificationService;
        private LoggingService _loggingService;

        public LoggingService LoggingService => _loggingService;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Initialize logging service first
            _loggingService = new LoggingService();
            _loggingService.LogInfo("Application starting up");

            // Set up global exception handling
            this.DispatcherUnhandledException += OnDispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

            // Initialize notification service
            _notificationService = new NotificationService();

            // Check for administrator privileges
            if (!IsRunningAsAdministrator())
            {
                MessageBox.Show(
                    "This application requires administrator privileges for full functionality.\n" +
                    "Some features may be limited without elevated permissions.",
                    "Administrator Privileges Required",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _loggingService?.LogInfo("Application shutting down");
            _notificationService?.Dispose();
            _loggingService?.Dispose();
            base.OnExit(e);
        }

        private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            LogException(e.Exception);

            MessageBox.Show(
                $"An unexpected error occurred:\n{e.Exception.Message}\n\nThe application will continue running.",
                "Error",
                MessageBoxButton.OK,
                MessageBoxImage.Error);

            e.Handled = true;
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception exception)
            {
                LogException(exception);

                MessageBox.Show(
                    $"A critical error occurred:\n{exception.Message}\n\nThe application will now exit.",
                    "Critical Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void LogException(Exception exception)
        {
            try
            {
                _loggingService?.LogError("Unhandled exception occurred", exception);
            }
            catch
            {
                // Ignore logging errors to prevent infinite loops
                System.Diagnostics.Debug.WriteLine($"Exception: {exception}");
            }
        }

        private bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }
    }
}
