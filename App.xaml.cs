using System;
using System.Windows;
using System.Windows.Threading;
using NetworkMonitor.Services;

namespace NetworkMonitor
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private NotificationService _notificationService;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Set up global exception handling
            this.DispatcherUnhandledException += OnDispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

            // Initialize notification service
            _notificationService = new NotificationService();

            // Check for administrator privileges
            if (!IsRunningAsAdministrator())
            {
                MessageBox.Show(
                    "This application requires administrator privileges for full functionality.\n" +
                    "Some features may be limited without elevated permissions.",
                    "Administrator Privileges Required",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _notificationService?.Dispose();
            base.OnExit(e);
        }

        private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            LogException(e.Exception);
            
            MessageBox.Show(
                $"An unexpected error occurred:\n{e.Exception.Message}\n\nThe application will continue running.",
                "Error",
                MessageBoxButton.OK,
                MessageBoxImage.Error);

            e.Handled = true;
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception exception)
            {
                LogException(exception);
                
                MessageBox.Show(
                    $"A critical error occurred:\n{exception.Message}\n\nThe application will now exit.",
                    "Critical Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        private void LogException(Exception exception)
        {
            try
            {
                // Log to file or event log
                System.Diagnostics.Debug.WriteLine($"Exception: {exception}");
                // TODO: Implement proper logging
            }
            catch
            {
                // Ignore logging errors to prevent infinite loops
            }
        }

        private bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }
    }
}
