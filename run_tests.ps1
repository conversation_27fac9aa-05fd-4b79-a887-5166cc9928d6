# PowerShell script to run NetworkMonitor tests
# This script builds the solution and runs all unit tests

param(
    [switch]$BuildOnly,
    [switch]$TestOnly,
    [string]$Configuration = "Debug"
)

Write-Host "NetworkMonitor Test Runner" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "NetworkMonitor.sln")) {
    Write-Error "NetworkMonitor.sln not found. Please run this script from the project root directory."
    exit 1
}

# Function to check if a command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Check for required tools
$hasVSBuild = Test-Command "msbuild"
$hasDotNet = Test-Command "dotnet"

if (-not $hasVSBuild -and -not $hasDotNet) {
    Write-Error "Neither MSBuild nor .NET CLI found. Please install Visual Studio or .NET SDK."
    exit 1
}

# Determine build tool
$buildTool = if ($hasDotNet) { "dotnet" } else { "msbuild" }
Write-Host "Using build tool: $buildTool" -ForegroundColor Yellow

# Build the solution
if (-not $TestOnly) {
    Write-Host "`nBuilding solution..." -ForegroundColor Cyan
    
    if ($buildTool -eq "dotnet") {
        $buildResult = & dotnet build NetworkMonitor.sln --configuration $Configuration --verbosity minimal
    } else {
        $buildResult = & msbuild NetworkMonitor.sln /p:Configuration=$Configuration /verbosity:minimal
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed. Please fix compilation errors and try again."
        exit 1
    }
    
    Write-Host "Build completed successfully!" -ForegroundColor Green
}

# Exit if build-only was requested
if ($BuildOnly) {
    Write-Host "Build-only mode. Exiting." -ForegroundColor Yellow
    exit 0
}

# Run tests
Write-Host "`nRunning tests..." -ForegroundColor Cyan

if (Test-Path "Tests/NetworkMonitor.Tests.csproj") {
    if ($buildTool -eq "dotnet") {
        Write-Host "Running tests with .NET CLI..." -ForegroundColor Yellow
        & dotnet test "Tests/NetworkMonitor.Tests.csproj" --configuration $Configuration --logger "console;verbosity=normal"
    } else {
        Write-Host "Running tests with MSTest..." -ForegroundColor Yellow
        $testDll = "Tests/bin/$Configuration/NetworkMonitor.Tests.dll"
        
        if (Test-Path $testDll) {
            if (Test-Command "vstest.console.exe") {
                & vstest.console.exe $testDll
            } else {
                Write-Warning "VSTest not found. Please run tests manually from Visual Studio."
            }
        } else {
            Write-Error "Test assembly not found at $testDll. Please build the solution first."
            exit 1
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nAll tests passed!" -ForegroundColor Green
    } else {
        Write-Host "`nSome tests failed. Please check the output above." -ForegroundColor Red
        exit 1
    }
} else {
    Write-Warning "Test project not found. Skipping tests."
}

Write-Host "`nTest run completed!" -ForegroundColor Green

# Optional: Generate test coverage report
if (Test-Command "dotnet" -and (Test-Path "Tests/NetworkMonitor.Tests.csproj")) {
    $generateCoverage = Read-Host "`nGenerate test coverage report? (y/N)"
    if ($generateCoverage -eq "y" -or $generateCoverage -eq "Y") {
        Write-Host "Generating coverage report..." -ForegroundColor Cyan
        
        # Install coverlet if not already installed
        & dotnet add "Tests/NetworkMonitor.Tests.csproj" package coverlet.msbuild --version 3.1.2
        
        # Run tests with coverage
        & dotnet test "Tests/NetworkMonitor.Tests.csproj" --configuration $Configuration --collect:"XPlat Code Coverage"
        
        Write-Host "Coverage report generated in TestResults folder." -ForegroundColor Green
    }
}

Write-Host "`nDone!" -ForegroundColor Green
