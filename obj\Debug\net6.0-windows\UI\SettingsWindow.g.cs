﻿#pragma checksum "..\..\..\..\UI\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CB362C6F8607F1EF75A470BAC5387F238EF21005"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace NetworkMonitor.UI {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 59 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UpdateIntervalComboBox;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DataRetentionTextBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartWithWindowsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MinimizeToTrayCheckBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SuspiciousConnectionAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HighBandwidthAlertsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BandwidthThresholdTextBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlertTimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AlwaysOnTopCheckBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MonitorSuspiciousConnectionsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LogSecurityEventsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BlockSuspiciousConnectionsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableLoggingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LogLevelComboBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogRetentionTextBox;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenLogFolderButton;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyButton;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\UI\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/NetworkMonitor;V1.0.0.0;component/ui/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UpdateIntervalComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.DataRetentionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.StartWithWindowsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 4:
            this.MinimizeToTrayCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.EnableAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.SuspiciousConnectionAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.HighBandwidthAlertsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.BandwidthThresholdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.AlertTimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.AlwaysOnTopCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.ShowNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.MonitorSuspiciousConnectionsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            this.LogSecurityEventsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.BlockSuspiciousConnectionsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.EnableLoggingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.LogLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.LogRetentionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.OpenLogFolderButton = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\..\UI\SettingsWindow.xaml"
            this.OpenLogFolderButton.Click += new System.Windows.RoutedEventHandler(this.OpenLogFolder_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 177 "..\..\..\..\UI\SettingsWindow.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.Ok_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 178 "..\..\..\..\UI\SettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.ApplyButton = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\UI\SettingsWindow.xaml"
            this.ApplyButton.Click += new System.Windows.RoutedEventHandler(this.Apply_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 180 "..\..\..\..\UI\SettingsWindow.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.Reset_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

