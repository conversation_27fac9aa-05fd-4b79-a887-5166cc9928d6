using System;
using System.IO;
using System.Text;
using System.Threading;

namespace NetworkMonitor.Services
{
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Critical
    }

    public class LoggingService : IDisposable
    {
        private readonly string _logDirectory;
        private readonly string _logFileName;
        private readonly object _lockObject = new object();
        private readonly Timer _cleanupTimer;
        private bool _disposed;

        public LoggingService()
        {
            _logDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "NetworkMonitor", "Logs");
            _logFileName = $"NetworkMonitor_{DateTime.Now:yyyyMMdd}.log";
            
            // Ensure log directory exists
            Directory.CreateDirectory(_logDirectory);
            
            // Setup cleanup timer to run daily
            _cleanupTimer = new Timer(CleanupOldLogs, null, TimeSpan.FromHours(1), TimeSpan.FromDays(1));
            
            // Log startup
            LogInfo("NetworkMonitor logging service started");
        }

        public void LogDebug(string message, Exception exception = null)
        {
            Log(LogLevel.Debug, message, exception);
        }

        public void LogInfo(string message, Exception exception = null)
        {
            Log(LogLevel.Info, message, exception);
        }

        public void LogWarning(string message, Exception exception = null)
        {
            Log(LogLevel.Warning, message, exception);
        }

        public void LogError(string message, Exception exception = null)
        {
            Log(LogLevel.Error, message, exception);
        }

        public void LogCritical(string message, Exception exception = null)
        {
            Log(LogLevel.Critical, message, exception);
        }

        private void Log(LogLevel level, string message, Exception exception = null)
        {
            if (_disposed) return;

            try
            {
                var logEntry = FormatLogEntry(level, message, exception);
                
                lock (_lockObject)
                {
                    var logFilePath = Path.Combine(_logDirectory, _logFileName);
                    File.AppendAllText(logFilePath, logEntry, Encoding.UTF8);
                }

                // Also write to debug output for development
                System.Diagnostics.Debug.WriteLine($"[{level}] {message}");
                if (exception != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Exception: {exception}");
                }
            }
            catch (Exception ex)
            {
                // Avoid infinite loops - just write to debug output
                System.Diagnostics.Debug.WriteLine($"Logging error: {ex.Message}");
            }
        }

        private string FormatLogEntry(LogLevel level, string message, Exception exception)
        {
            var sb = new StringBuilder();
            sb.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}");
            
            if (exception != null)
            {
                sb.AppendLine($"Exception: {exception.GetType().Name}: {exception.Message}");
                sb.AppendLine($"Stack Trace: {exception.StackTrace}");
                
                // Log inner exceptions
                var innerException = exception.InnerException;
                while (innerException != null)
                {
                    sb.AppendLine($"Inner Exception: {innerException.GetType().Name}: {innerException.Message}");
                    sb.AppendLine($"Inner Stack Trace: {innerException.StackTrace}");
                    innerException = innerException.InnerException;
                }
            }
            
            sb.AppendLine(); // Empty line for readability
            return sb.ToString();
        }

        private void CleanupOldLogs(object state)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-30); // Keep logs for 30 days
                var logFiles = Directory.GetFiles(_logDirectory, "NetworkMonitor_*.log");
                
                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                        LogInfo($"Deleted old log file: {Path.GetFileName(logFile)}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("Error during log cleanup", ex);
            }
        }

        public string GetLogDirectory()
        {
            return _logDirectory;
        }

        public void Dispose()
        {
            if (_disposed) return;
            
            _disposed = true;
            _cleanupTimer?.Dispose();
            
            LogInfo("NetworkMonitor logging service stopped");
        }
    }
}
