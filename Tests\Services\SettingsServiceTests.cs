using System;
using System.IO;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using NetworkMonitor.Services;

namespace NetworkMonitor.Tests.Services
{
    [TestClass]
    public class SettingsServiceTests
    {
        private SettingsService _settingsService;
        private string _testSettingsDirectory;
        private string _originalAppDataPath;

        [TestInitialize]
        public void Setup()
        {
            _testSettingsDirectory = Path.Combine(Path.GetTempPath(), "NetworkMonitorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testSettingsDirectory);
            
            _settingsService = new SettingsService();
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (Directory.Exists(_testSettingsDirectory))
            {
                Directory.Delete(_testSettingsDirectory, true);
            }
        }

        [TestMethod]
        public void LoadSettings_WithNoExistingFile_ShouldReturnDefaults()
        {
            // Act
            var settings = _settingsService.LoadSettings();

            // Assert
            Assert.IsNotNull(settings);
            Assert.AreEqual(5000, settings.UpdateInterval);
            Assert.AreEqual(30, settings.DataRetentionDays);
            Assert.AreEqual(false, settings.StartWithWindows);
            Assert.AreEqual(true, settings.MinimizeToTray);
            Assert.AreEqual(true, settings.EnableAlerts);
            Assert.AreEqual("Light", settings.Theme);
            Assert.AreEqual("Info", settings.LogLevel);
        }

        [TestMethod]
        public void SaveSettings_ShouldPersistSettings()
        {
            // Arrange
            var testSettings = new AppSettings
            {
                UpdateInterval = 10000,
                DataRetentionDays = 60,
                StartWithWindows = true,
                MinimizeToTray = false,
                EnableAlerts = false,
                Theme = "Dark",
                LogLevel = "Debug"
            };

            // Act
            _settingsService.SaveSettings(testSettings);
            var loadedSettings = _settingsService.LoadSettings();

            // Assert
            Assert.AreEqual(testSettings.UpdateInterval, loadedSettings.UpdateInterval);
            Assert.AreEqual(testSettings.DataRetentionDays, loadedSettings.DataRetentionDays);
            Assert.AreEqual(testSettings.StartWithWindows, loadedSettings.StartWithWindows);
            Assert.AreEqual(testSettings.MinimizeToTray, loadedSettings.MinimizeToTray);
            Assert.AreEqual(testSettings.EnableAlerts, loadedSettings.EnableAlerts);
            Assert.AreEqual(testSettings.Theme, loadedSettings.Theme);
            Assert.AreEqual(testSettings.LogLevel, loadedSettings.LogLevel);
        }

        [TestMethod]
        public void GetCurrentSettings_ShouldReturnCachedSettings()
        {
            // Arrange
            var testSettings = new AppSettings
            {
                UpdateInterval = 15000,
                Theme = "Dark"
            };
            _settingsService.SaveSettings(testSettings);

            // Act
            var currentSettings = _settingsService.GetCurrentSettings();

            // Assert
            Assert.IsNotNull(currentSettings);
            Assert.AreEqual(15000, currentSettings.UpdateInterval);
            Assert.AreEqual("Dark", currentSettings.Theme);
        }

        [TestMethod]
        public void ResetToDefaults_ShouldRestoreDefaultValues()
        {
            // Arrange
            var modifiedSettings = new AppSettings
            {
                UpdateInterval = 20000,
                DataRetentionDays = 90,
                Theme = "Dark",
                LogLevel = "Error"
            };
            _settingsService.SaveSettings(modifiedSettings);

            // Act
            _settingsService.ResetToDefaults();
            var resetSettings = _settingsService.GetCurrentSettings();

            // Assert
            Assert.AreEqual(5000, resetSettings.UpdateInterval);
            Assert.AreEqual(30, resetSettings.DataRetentionDays);
            Assert.AreEqual("Light", resetSettings.Theme);
            Assert.AreEqual("Info", resetSettings.LogLevel);
        }

        [TestMethod]
        public void SaveSettings_WithInvalidValues_ShouldValidateAndCorrect()
        {
            // Arrange
            var invalidSettings = new AppSettings
            {
                UpdateInterval = 100, // Too low
                DataRetentionDays = 500, // Too high
                BandwidthThresholdMBps = -5, // Negative
                AlertTimeoutSeconds = 100, // Too high
                Theme = "InvalidTheme",
                LogLevel = "InvalidLevel"
            };

            // Act
            _settingsService.SaveSettings(invalidSettings);
            var correctedSettings = _settingsService.GetCurrentSettings();

            // Assert
            Assert.AreEqual(5000, correctedSettings.UpdateInterval); // Corrected to default
            Assert.AreEqual(365, correctedSettings.DataRetentionDays); // Corrected to max
            Assert.AreEqual(10.0, correctedSettings.BandwidthThresholdMBps); // Corrected to default
            Assert.AreEqual(60, correctedSettings.AlertTimeoutSeconds); // Corrected to max
            Assert.AreEqual("Light", correctedSettings.Theme); // Corrected to default
            Assert.AreEqual("Info", correctedSettings.LogLevel); // Corrected to default
        }

        [TestMethod]
        public void GetSettingsFilePath_ShouldReturnValidPath()
        {
            // Act
            var filePath = _settingsService.GetSettingsFilePath();

            // Assert
            Assert.IsFalse(string.IsNullOrEmpty(filePath));
            Assert.IsTrue(filePath.EndsWith("settings.json"));
        }

        [TestMethod]
        public void ExportSettings_ShouldCreateFile()
        {
            // Arrange
            var exportPath = Path.Combine(_testSettingsDirectory, "exported_settings.json");
            var testSettings = new AppSettings { UpdateInterval = 7500 };
            _settingsService.SaveSettings(testSettings);

            // Act
            _settingsService.ExportSettings(exportPath);

            // Assert
            Assert.IsTrue(File.Exists(exportPath));
            var exportedContent = File.ReadAllText(exportPath);
            Assert.IsTrue(exportedContent.Contains("7500"));
        }

        [TestMethod]
        public void ImportSettings_ShouldLoadFromFile()
        {
            // Arrange
            var importPath = Path.Combine(_testSettingsDirectory, "import_settings.json");
            var testSettings = new AppSettings { UpdateInterval = 12000, Theme = "Dark" };
            _settingsService.SaveSettings(testSettings);
            _settingsService.ExportSettings(importPath);

            // Reset to defaults
            _settingsService.ResetToDefaults();

            // Act
            _settingsService.ImportSettings(importPath);
            var importedSettings = _settingsService.GetCurrentSettings();

            // Assert
            Assert.AreEqual(12000, importedSettings.UpdateInterval);
            Assert.AreEqual("Dark", importedSettings.Theme);
        }
    }
}
