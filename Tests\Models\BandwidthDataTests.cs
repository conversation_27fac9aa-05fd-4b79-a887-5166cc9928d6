using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using NetworkMonitor.Models;

namespace NetworkMonitor.Tests.Models
{
    [TestClass]
    public class BandwidthDataTests
    {
        [TestMethod]
        public void BandwidthData_Constructor_ShouldInitializeProperties()
        {
            // Arrange
            var timestamp = DateTime.Now;
            var downloadSpeed = 1024.0 * 1024; // 1 MB/s
            var uploadSpeed = 512.0 * 1024;    // 0.5 MB/s
            var totalDownloaded = 100L * 1024 * 1024; // 100 MB
            var totalUploaded = 50L * 1024 * 1024;    // 50 MB

            // Act
            var bandwidthData = new BandwidthData
            {
                Timestamp = timestamp,
                DownloadSpeed = downloadSpeed,
                UploadSpeed = uploadSpeed,
                TotalDownloaded = totalDownloaded,
                TotalUploaded = totalUploaded
            };

            // Assert
            Assert.AreEqual(timestamp, bandwidthData.Timestamp);
            Assert.AreEqual(downloadSpeed, bandwidthData.DownloadSpeed);
            Assert.AreEqual(uploadSpeed, bandwidthData.UploadSpeed);
            Assert.AreEqual(totalDownloaded, bandwidthData.TotalDownloaded);
            Assert.AreEqual(totalUploaded, bandwidthData.TotalUploaded);
        }

        [TestMethod]
        public void DownloadSpeedFormatted_ShouldFormatCorrectly()
        {
            // Arrange
            var bandwidthData = new BandwidthData
            {
                DownloadSpeed = 1536 * 1024 // 1.5 MB/s
            };

            // Act
            var formatted = bandwidthData.DownloadSpeedFormatted;

            // Assert
            Assert.IsTrue(formatted.Contains("1.5"));
            Assert.IsTrue(formatted.Contains("MB/s"));
        }

        [TestMethod]
        public void UploadSpeedFormatted_ShouldFormatCorrectly()
        {
            // Arrange
            var bandwidthData = new BandwidthData
            {
                UploadSpeed = 768 * 1024 // 0.75 MB/s
            };

            // Act
            var formatted = bandwidthData.UploadSpeedFormatted;

            // Assert
            Assert.IsTrue(formatted.Contains("0.8") || formatted.Contains("0.75"));
            Assert.IsTrue(formatted.Contains("MB/s"));
        }

        [TestMethod]
        public void TotalDownloadedFormatted_ShouldFormatLargeValues()
        {
            // Arrange
            var bandwidthData = new BandwidthData
            {
                TotalDownloaded = 2L * 1024 * 1024 * 1024 // 2 GB
            };

            // Act
            var formatted = bandwidthData.TotalDownloadedFormatted;

            // Assert
            Assert.IsTrue(formatted.Contains("2.0"));
            Assert.IsTrue(formatted.Contains("GB"));
        }

        [TestMethod]
        public void TotalUploadedFormatted_ShouldFormatSmallValues()
        {
            // Arrange
            var bandwidthData = new BandwidthData
            {
                TotalUploaded = 512 * 1024 // 512 KB
            };

            // Act
            var formatted = bandwidthData.TotalUploadedFormatted;

            // Assert
            Assert.IsTrue(formatted.Contains("512"));
            Assert.IsTrue(formatted.Contains("KB"));
        }

        [TestMethod]
        public void TotalSpeedFormatted_ShouldCombineDownloadAndUpload()
        {
            // Arrange
            var bandwidthData = new BandwidthData
            {
                DownloadSpeed = 1024 * 1024, // 1 MB/s
                UploadSpeed = 512 * 1024     // 0.5 MB/s
            };

            // Act
            var formatted = bandwidthData.TotalSpeedFormatted;

            // Assert
            Assert.IsTrue(formatted.Contains("1.5"));
            Assert.IsTrue(formatted.Contains("MB/s"));
        }

        [TestMethod]
        public void PropertyChanged_ShouldFireWhenDownloadSpeedChanges()
        {
            // Arrange
            var bandwidthData = new BandwidthData();
            var propertyChangedFired = false;
            string changedPropertyName = null;

            bandwidthData.PropertyChanged += (sender, e) =>
            {
                propertyChangedFired = true;
                changedPropertyName = e.PropertyName;
            };

            // Act
            bandwidthData.DownloadSpeed = 1024;

            // Assert
            Assert.IsTrue(propertyChangedFired);
            Assert.AreEqual(nameof(BandwidthData.DownloadSpeed), changedPropertyName);
        }

        [TestMethod]
        public void PropertyChanged_ShouldFireWhenUploadSpeedChanges()
        {
            // Arrange
            var bandwidthData = new BandwidthData();
            var propertyChangedFired = false;
            string changedPropertyName = null;

            bandwidthData.PropertyChanged += (sender, e) =>
            {
                propertyChangedFired = true;
                changedPropertyName = e.PropertyName;
            };

            // Act
            bandwidthData.UploadSpeed = 512;

            // Assert
            Assert.IsTrue(propertyChangedFired);
            Assert.AreEqual(nameof(BandwidthData.UploadSpeed), changedPropertyName);
        }
    }
}
