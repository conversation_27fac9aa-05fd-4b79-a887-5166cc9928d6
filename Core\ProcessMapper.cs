using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using NetworkMonitor.Models;

namespace NetworkMonitor.Core
{
    public class ProcessMapper
    {
        private readonly Dictionary<int, ProcessInfo> _processCache = new Dictionary<int, ProcessInfo>();
        private readonly Dictionary<int, ApplicationUsage> _applicationUsage = new Dictionary<int, ApplicationUsage>();
        private DateTime _lastCacheUpdate = DateTime.MinValue;
        private readonly TimeSpan _cacheTimeout = TimeSpan.FromMinutes(5);

        public class ProcessInfo
        {
            public int ProcessId { get; set; }
            public string ProcessName { get; set; }
            public string ExecutablePath { get; set; }
            public string CommandLine { get; set; }
            public DateTime StartTime { get; set; }
            public string Company { get; set; }
            public string Description { get; set; }
            public string Version { get; set; }
            public long WorkingSet { get; set; }
            public double CpuUsage { get; set; }
        }

        public ProcessInfo GetProcessInfo(int processId)
        {
            // Check cache first
            if (_processCache.ContainsKey(processId) && 
                DateTime.Now - _lastCacheUpdate < _cacheTimeout)
            {
                return _processCache[processId];
            }

            // Refresh cache if needed
            if (DateTime.Now - _lastCacheUpdate >= _cacheTimeout)
            {
                RefreshProcessCache();
            }

            return _processCache.GetValueOrDefault(processId, CreateUnknownProcessInfo(processId));
        }

        public ApplicationUsage GetApplicationUsage(int processId)
        {
            if (!_applicationUsage.ContainsKey(processId))
            {
                var processInfo = GetProcessInfo(processId);
                _applicationUsage[processId] = new ApplicationUsage
                {
                    ProcessId = processId,
                    ProcessName = processInfo.ProcessName,
                    ProcessPath = processInfo.ExecutablePath,
                    FirstSeen = DateTime.Now,
                    LastSeen = DateTime.Now
                };
            }

            return _applicationUsage[processId];
        }

        public void UpdateApplicationUsage(NetworkConnection connection)
        {
            var usage = GetApplicationUsage(connection.ProcessId);
            usage.LastSeen = connection.LastSeen;
            usage.BytesReceived += connection.BytesReceived;
            usage.BytesSent += connection.BytesSent;
        }

        public IEnumerable<ApplicationUsage> GetTopApplicationsByUsage(int count = 10)
        {
            return _applicationUsage.Values
                .OrderByDescending(a => a.BytesReceived + a.BytesSent)
                .Take(count);
        }

        public IEnumerable<ApplicationUsage> GetApplicationsWithHighBandwidth(double thresholdBytesPerSecond)
        {
            return _applicationUsage.Values
                .Where(a => a.CurrentDownloadSpeed + a.CurrentUploadSpeed > thresholdBytesPerSecond);
        }

        private void RefreshProcessCache()
        {
            try
            {
                _processCache.Clear();

                // Get basic process information
                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        var processInfo = CreateProcessInfo(process);
                        _processCache[process.Id] = processInfo;
                    }
                    catch (Exception ex)
                    {
                        // Some processes may not be accessible
                        Debug.WriteLine($"Error getting info for process {process.Id}: {ex.Message}");
                        _processCache[process.Id] = CreateUnknownProcessInfo(process.Id);
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                // Enhance with WMI data
                EnhanceWithWmiData();

                _lastCacheUpdate = DateTime.Now;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing process cache: {ex.Message}");
            }
        }

        private ProcessInfo CreateProcessInfo(Process process)
        {
            var processInfo = new ProcessInfo
            {
                ProcessId = process.Id,
                ProcessName = process.ProcessName,
                StartTime = GetProcessStartTime(process),
                WorkingSet = GetProcessWorkingSet(process)
            };

            try
            {
                processInfo.ExecutablePath = process.MainModule?.FileName ?? "";
                
                if (!string.IsNullOrEmpty(processInfo.ExecutablePath) && File.Exists(processInfo.ExecutablePath))
                {
                    var versionInfo = FileVersionInfo.GetVersionInfo(processInfo.ExecutablePath);
                    processInfo.Company = versionInfo.CompanyName ?? "";
                    processInfo.Description = versionInfo.FileDescription ?? "";
                    processInfo.Version = versionInfo.FileVersion ?? "";
                }
            }
            catch
            {
                // Some processes may not allow access to their modules
            }

            return processInfo;
        }

        private ProcessInfo CreateUnknownProcessInfo(int processId)
        {
            return new ProcessInfo
            {
                ProcessId = processId,
                ProcessName = $"PID {processId}",
                ExecutablePath = "",
                CommandLine = "",
                StartTime = DateTime.MinValue,
                Company = "",
                Description = "",
                Version = "",
                WorkingSet = 0,
                CpuUsage = 0
            };
        }

        private DateTime GetProcessStartTime(Process process)
        {
            try
            {
                return process.StartTime;
            }
            catch
            {
                return DateTime.MinValue;
            }
        }

        private long GetProcessWorkingSet(Process process)
        {
            try
            {
                return process.WorkingSet64;
            }
            catch
            {
                return 0;
            }
        }

        private void EnhanceWithWmiData()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher(
                    "SELECT ProcessId, CommandLine, PageFileUsage FROM Win32_Process");
                
                foreach (ManagementObject obj in searcher.Get())
                {
                    try
                    {
                        var processId = Convert.ToInt32(obj["ProcessId"]);
                        if (_processCache.ContainsKey(processId))
                        {
                            var processInfo = _processCache[processId];
                            processInfo.CommandLine = obj["CommandLine"]?.ToString() ?? "";
                        }
                    }
                    catch
                    {
                        // Ignore errors for individual processes
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error enhancing with WMI data: {ex.Message}");
            }
        }

        public IEnumerable<ProcessInfo> GetAllProcesses()
        {
            if (DateTime.Now - _lastCacheUpdate >= _cacheTimeout)
            {
                RefreshProcessCache();
            }

            return _processCache.Values;
        }

        public IEnumerable<ProcessInfo> GetProcessesByName(string processName)
        {
            return GetAllProcesses()
                .Where(p => p.ProcessName.IndexOf(processName, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        public IEnumerable<ProcessInfo> GetProcessesByPath(string path)
        {
            return GetAllProcesses()
                .Where(p => p.ExecutablePath.IndexOf(path, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        public bool IsSystemProcess(int processId)
        {
            if (processId == 0 || processId == 4) return true;

            var processInfo = GetProcessInfo(processId);
            
            // Check if it's a Windows system process
            var systemPaths = new[]
            {
                @"C:\Windows\System32",
                @"C:\Windows\SysWOW64",
                @"C:\Windows\WinSxS"
            };

            return systemPaths.Any(path => 
                processInfo.ExecutablePath.StartsWith(path, StringComparison.OrdinalIgnoreCase));
        }

        public bool IsSuspiciousProcess(int processId)
        {
            var processInfo = GetProcessInfo(processId);
            
            // Basic heuristics for suspicious processes
            if (string.IsNullOrEmpty(processInfo.ExecutablePath))
                return true;

            if (string.IsNullOrEmpty(processInfo.Company) && !IsSystemProcess(processId))
                return true;

            // Check for processes running from temp directories
            var suspiciousPaths = new[]
            {
                @"\Temp\",
                @"\AppData\Local\Temp\",
                @"\Users\Public\"
            };

            return suspiciousPaths.Any(path => 
                processInfo.ExecutablePath.IndexOf(path, StringComparison.OrdinalIgnoreCase) >= 0);
        }

        public void ClearCache()
        {
            _processCache.Clear();
            _lastCacheUpdate = DateTime.MinValue;
        }

        public void ClearApplicationUsage()
        {
            _applicationUsage.Clear();
        }

        public Dictionary<string, int> GetProcessStatistics()
        {
            var stats = new Dictionary<string, int>();
            
            var processes = GetAllProcesses().ToList();
            stats["TotalProcesses"] = processes.Count;
            stats["SystemProcesses"] = processes.Count(p => IsSystemProcess(p.ProcessId));
            stats["SuspiciousProcesses"] = processes.Count(p => IsSuspiciousProcess(p.ProcessId));
            stats["ProcessesWithConnections"] = _applicationUsage.Count;
            
            return stats;
        }
    }
}
