using System;
using System.IO;
using Newtonsoft.Json;

namespace NetworkMonitor.Services
{
    public class AppSettings
    {
        // Monitoring Settings
        public int UpdateInterval { get; set; } = 5000; // milliseconds
        public int DataRetentionDays { get; set; } = 30;
        public bool StartWithWindows { get; set; } = false;
        public bool MinimizeToTray { get; set; } = true;

        // Alert Settings
        public bool EnableAlerts { get; set; } = true;
        public bool SuspiciousConnectionAlerts { get; set; } = true;
        public bool HighBandwidthAlerts { get; set; } = true;
        public double BandwidthThresholdMBps { get; set; } = 10.0;
        public int AlertTimeoutSeconds { get; set; } = 5;

        // Display Settings
        public bool AlwaysOnTop { get; set; } = false;
        public bool ShowNotifications { get; set; } = true;
        public string Theme { get; set; } = "Light";

        // Security Settings
        public bool MonitorSuspiciousConnections { get; set; } = true;
        public bool LogSecurityEvents { get; set; } = true;
        public bool BlockSuspiciousConnections { get; set; } = false;

        // Logging Settings
        public bool EnableLogging { get; set; } = true;
        public string LogLevel { get; set; } = "Info";
        public int LogRetentionDays { get; set; } = 30;
    }

    public class SettingsService
    {
        private readonly string _settingsDirectory;
        private readonly string _settingsFilePath;
        private AppSettings _currentSettings;

        public SettingsService()
        {
            _settingsDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "NetworkMonitor");
            _settingsFilePath = Path.Combine(_settingsDirectory, "settings.json");
            
            // Ensure settings directory exists
            Directory.CreateDirectory(_settingsDirectory);
            
            // Load settings on initialization
            _currentSettings = LoadSettings();
        }

        public AppSettings LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    var json = File.ReadAllText(_settingsFilePath);
                    var settings = JsonConvert.DeserializeObject<AppSettings>(json);
                    
                    // Validate settings and apply defaults for any missing values
                    return ValidateSettings(settings ?? new AppSettings());
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
            }

            // Return default settings if loading fails
            return new AppSettings();
        }

        public void SaveSettings(AppSettings settings)
        {
            try
            {
                var validatedSettings = ValidateSettings(settings);
                var json = JsonConvert.SerializeObject(validatedSettings, Formatting.Indented);
                File.WriteAllText(_settingsFilePath, json);
                _currentSettings = validatedSettings;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to save settings: {ex.Message}", ex);
            }
        }

        public AppSettings GetCurrentSettings()
        {
            return _currentSettings ?? LoadSettings();
        }

        public void ResetToDefaults()
        {
            _currentSettings = new AppSettings();
            SaveSettings(_currentSettings);
        }

        private AppSettings ValidateSettings(AppSettings settings)
        {
            // Validate and correct any invalid values
            if (settings.UpdateInterval < 1000) settings.UpdateInterval = 5000;
            if (settings.UpdateInterval > 60000) settings.UpdateInterval = 60000;
            
            if (settings.DataRetentionDays < 1) settings.DataRetentionDays = 30;
            if (settings.DataRetentionDays > 365) settings.DataRetentionDays = 365;
            
            if (settings.BandwidthThresholdMBps < 0.1) settings.BandwidthThresholdMBps = 10.0;
            if (settings.BandwidthThresholdMBps > 1000) settings.BandwidthThresholdMBps = 1000;
            
            if (settings.AlertTimeoutSeconds < 1) settings.AlertTimeoutSeconds = 5;
            if (settings.AlertTimeoutSeconds > 60) settings.AlertTimeoutSeconds = 60;
            
            if (settings.LogRetentionDays < 1) settings.LogRetentionDays = 30;
            if (settings.LogRetentionDays > 365) settings.LogRetentionDays = 365;
            
            // Validate string values
            if (string.IsNullOrEmpty(settings.Theme) || 
                (settings.Theme != "Light" && settings.Theme != "Dark" && settings.Theme != "System"))
            {
                settings.Theme = "Light";
            }
            
            if (string.IsNullOrEmpty(settings.LogLevel) ||
                (settings.LogLevel != "Debug" && settings.LogLevel != "Info" && 
                 settings.LogLevel != "Warning" && settings.LogLevel != "Error"))
            {
                settings.LogLevel = "Info";
            }
            
            return settings;
        }

        public string GetSettingsFilePath()
        {
            return _settingsFilePath;
        }

        public void ExportSettings(string filePath)
        {
            try
            {
                var json = JsonConvert.SerializeObject(_currentSettings, Formatting.Indented);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to export settings: {ex.Message}", ex);
            }
        }

        public void ImportSettings(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("Settings file not found.");
                }

                var json = File.ReadAllText(filePath);
                var importedSettings = JsonConvert.DeserializeObject<AppSettings>(json);
                
                if (importedSettings != null)
                {
                    SaveSettings(importedSettings);
                }
                else
                {
                    throw new Exception("Invalid settings file format.");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to import settings: {ex.Message}", ex);
            }
        }
    }
}
