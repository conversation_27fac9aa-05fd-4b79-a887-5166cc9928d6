using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Management;
using System.Threading;
using System.Threading.Tasks;
using NetworkMonitor.Models;

namespace NetworkMonitor.Core
{
    public class BandwidthMonitor : INotifyPropertyChanged, IDisposable
    {
        private readonly Timer _updateTimer;
        private readonly Dictionary<string, PerformanceCounter> _downloadCounters = new Dictionary<string, PerformanceCounter>();
        private readonly Dictionary<string, PerformanceCounter> _uploadCounters = new Dictionary<string, PerformanceCounter>();
        private readonly Dictionary<string, long> _previousDownloadValues = new Dictionary<string, long>();
        private readonly Dictionary<string, long> _previousUploadValues = new Dictionary<string, long>();
        private readonly Dictionary<string, DateTime> _lastUpdateTimes = new Dictionary<string, DateTime>();
        private readonly object _lockObject = new object();
        private bool _isRunning;
        private bool _disposed;

        public ObservableCollection<BandwidthData> BandwidthHistory { get; }
        public ObservableCollection<NetworkInterface> NetworkInterfaces { get; }
        
        private BandwidthData _currentBandwidth;
        public BandwidthData CurrentBandwidth
        {
            get => _currentBandwidth;
            private set
            {
                _currentBandwidth = value;
                OnPropertyChanged(nameof(CurrentBandwidth));
            }
        }

        private NetworkInterface _selectedInterface;
        public NetworkInterface SelectedInterface
        {
            get => _selectedInterface;
            set
            {
                _selectedInterface = value;
                OnPropertyChanged(nameof(SelectedInterface));
                RefreshCounters();
            }
        }

        public bool IsRunning
        {
            get => _isRunning;
            private set
            {
                _isRunning = value;
                OnPropertyChanged(nameof(IsRunning));
            }
        }

        public int UpdateInterval { get; set; } = 1000; // 1 second
        public int HistoryRetentionHours { get; set; } = 24;

        public BandwidthMonitor()
        {
            BandwidthHistory = new ObservableCollection<BandwidthData>();
            NetworkInterfaces = new ObservableCollection<NetworkInterface>();
            _updateTimer = new Timer(UpdateBandwidth, null, Timeout.Infinite, Timeout.Infinite);
            
            InitializeNetworkInterfaces();
        }

        public void Start()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(BandwidthMonitor));
            
            IsRunning = true;
            _updateTimer.Change(0, UpdateInterval);
        }

        public void Stop()
        {
            IsRunning = false;
            _updateTimer.Change(Timeout.Infinite, Timeout.Infinite);
        }

        private void InitializeNetworkInterfaces()
        {
            try
            {
                var category = new PerformanceCounterCategory("Network Interface");
                var instanceNames = category.GetInstanceNames();

                foreach (var instanceName in instanceNames)
                {
                    if (instanceName == "Loopback Pseudo-Interface 1" || 
                        instanceName.Contains("Isatap") || 
                        instanceName.Contains("Teredo"))
                        continue;

                    try
                    {
                        var networkInterface = new NetworkInterface
                        {
                            Name = instanceName,
                            Description = GetInterfaceDescription(instanceName),
                            Type = GetInterfaceType(instanceName),
                            Speed = GetInterfaceSpeed(instanceName),
                            MacAddress = GetMacAddress(instanceName),
                            IsActive = true,
                            Status = "Connected"
                        };

                        NetworkInterfaces.Add(networkInterface);

                        // Create performance counters for this interface
                        var downloadCounter = new PerformanceCounter("Network Interface", "Bytes Received/sec", instanceName);
                        var uploadCounter = new PerformanceCounter("Network Interface", "Bytes Sent/sec", instanceName);
                        
                        _downloadCounters[instanceName] = downloadCounter;
                        _uploadCounters[instanceName] = uploadCounter;
                        
                        // Initialize counters
                        downloadCounter.NextValue();
                        uploadCounter.NextValue();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error initializing interface {instanceName}: {ex.Message}");
                    }
                }

                // Select the first active interface by default
                SelectedInterface = NetworkInterfaces.FirstOrDefault();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing network interfaces: {ex.Message}");
            }
        }

        private string GetInterfaceDescription(string instanceName)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher(
                    "SELECT * FROM Win32_NetworkAdapter WHERE NetConnectionID = '" + instanceName + "'");
                
                foreach (ManagementObject obj in searcher.Get())
                {
                    return obj["Description"]?.ToString() ?? instanceName;
                }
            }
            catch
            {
                // Ignore errors
            }
            
            return instanceName;
        }

        private string GetInterfaceType(string instanceName)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher(
                    "SELECT * FROM Win32_NetworkAdapter WHERE NetConnectionID = '" + instanceName + "'");
                
                foreach (ManagementObject obj in searcher.Get())
                {
                    var adapterType = obj["AdapterType"]?.ToString();
                    if (!string.IsNullOrEmpty(adapterType))
                        return adapterType;
                }
            }
            catch
            {
                // Ignore errors
            }
            
            return "Unknown";
        }

        private long GetInterfaceSpeed(string instanceName)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher(
                    "SELECT * FROM Win32_NetworkAdapter WHERE NetConnectionID = '" + instanceName + "'");
                
                foreach (ManagementObject obj in searcher.Get())
                {
                    var speed = obj["Speed"];
                    if (speed != null && long.TryParse(speed.ToString(), out long speedValue))
                        return speedValue;
                }
            }
            catch
            {
                // Ignore errors
            }
            
            return 0;
        }

        private string GetMacAddress(string instanceName)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher(
                    "SELECT * FROM Win32_NetworkAdapter WHERE NetConnectionID = '" + instanceName + "'");
                
                foreach (ManagementObject obj in searcher.Get())
                {
                    return obj["MACAddress"]?.ToString() ?? "";
                }
            }
            catch
            {
                // Ignore errors
            }
            
            return "";
        }

        private void RefreshCounters()
        {
            if (SelectedInterface == null) return;

            try
            {
                var instanceName = SelectedInterface.Name;
                
                if (_downloadCounters.ContainsKey(instanceName) && _uploadCounters.ContainsKey(instanceName))
                {
                    // Reset previous values when switching interfaces
                    _previousDownloadValues.Remove(instanceName);
                    _previousUploadValues.Remove(instanceName);
                    _lastUpdateTimes.Remove(instanceName);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing counters: {ex.Message}");
            }
        }

        private async void UpdateBandwidth(object state)
        {
            if (!IsRunning || _disposed || SelectedInterface == null) return;

            try
            {
                await Task.Run(() =>
                {
                    var bandwidthData = CalculateBandwidth();
                    if (bandwidthData != null)
                    {
                        System.Windows.Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            CurrentBandwidth = bandwidthData;
                            BandwidthHistory.Add(bandwidthData);
                            
                            // Clean up old history
                            CleanupHistory();
                        }));
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating bandwidth: {ex.Message}");
            }
        }

        private BandwidthData CalculateBandwidth()
        {
            if (SelectedInterface == null) return null;

            var instanceName = SelectedInterface.Name;
            
            if (!_downloadCounters.ContainsKey(instanceName) || !_uploadCounters.ContainsKey(instanceName))
                return null;

            try
            {
                var downloadCounter = _downloadCounters[instanceName];
                var uploadCounter = _uploadCounters[instanceName];
                
                var currentDownload = (long)downloadCounter.NextValue();
                var currentUpload = (long)uploadCounter.NextValue();
                var currentTime = DateTime.Now;

                double downloadSpeed = 0;
                double uploadSpeed = 0;

                if (_previousDownloadValues.ContainsKey(instanceName) && _lastUpdateTimes.ContainsKey(instanceName))
                {
                    var timeDiff = (currentTime - _lastUpdateTimes[instanceName]).TotalSeconds;
                    if (timeDiff > 0)
                    {
                        var downloadDiff = currentDownload - _previousDownloadValues[instanceName];
                        var uploadDiff = currentUpload - _previousUploadValues[instanceName];
                        
                        downloadSpeed = Math.Max(0, downloadDiff / timeDiff);
                        uploadSpeed = Math.Max(0, uploadDiff / timeDiff);
                    }
                }

                _previousDownloadValues[instanceName] = currentDownload;
                _previousUploadValues[instanceName] = currentUpload;
                _lastUpdateTimes[instanceName] = currentTime;

                return new BandwidthData
                {
                    Timestamp = currentTime,
                    InterfaceName = instanceName,
                    InterfaceDescription = SelectedInterface.Description,
                    DownloadSpeed = downloadSpeed,
                    UploadSpeed = uploadSpeed,
                    TotalDownloaded = currentDownload,
                    TotalUploaded = currentUpload
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error calculating bandwidth for {instanceName}: {ex.Message}");
                return null;
            }
        }

        private void CleanupHistory()
        {
            var cutoffTime = DateTime.Now.AddHours(-HistoryRetentionHours);
            
            lock (_lockObject)
            {
                var itemsToRemove = BandwidthHistory.Where(b => b.Timestamp < cutoffTime).ToList();
                foreach (var item in itemsToRemove)
                {
                    BandwidthHistory.Remove(item);
                }
            }
        }

        public BandwidthData GetPeakBandwidth(TimeSpan period)
        {
            var cutoffTime = DateTime.Now - period;
            var relevantData = BandwidthHistory.Where(b => b.Timestamp >= cutoffTime).ToList();
            
            if (!relevantData.Any()) return null;

            var peak = relevantData.OrderByDescending(b => b.TotalSpeed).First();
            return peak;
        }

        public double GetAverageBandwidth(TimeSpan period)
        {
            var cutoffTime = DateTime.Now - period;
            var relevantData = BandwidthHistory.Where(b => b.Timestamp >= cutoffTime).ToList();
            
            if (!relevantData.Any()) return 0;

            return relevantData.Average(b => b.TotalSpeed);
        }

        public long GetTotalDataTransferred(TimeSpan period)
        {
            var cutoffTime = DateTime.Now - period;
            var relevantData = BandwidthHistory.Where(b => b.Timestamp >= cutoffTime).ToList();
            
            if (!relevantData.Any()) return 0;

            var first = relevantData.OrderBy(b => b.Timestamp).First();
            var last = relevantData.OrderByDescending(b => b.Timestamp).First();
            
            return (last.TotalDownloaded + last.TotalUploaded) - (first.TotalDownloaded + first.TotalUploaded);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            if (_disposed) return;
            
            Stop();
            _updateTimer?.Dispose();
            
            foreach (var counter in _downloadCounters.Values)
                counter?.Dispose();
            foreach (var counter in _uploadCounters.Values)
                counter?.Dispose();
                
            _downloadCounters.Clear();
            _uploadCounters.Clear();
            
            _disposed = true;
        }
    }
}
