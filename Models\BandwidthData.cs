using System;
using System.ComponentModel;

namespace NetworkMonitor.Models
{
    public class BandwidthData : INotifyPropertyChanged
    {
        private double _downloadSpeed;
        private double _uploadSpeed;
        private long _totalDownloaded;
        private long _totalUploaded;

        public DateTime Timestamp { get; set; }
        public string InterfaceName { get; set; }
        public string InterfaceDescription { get; set; }

        public double DownloadSpeed
        {
            get => _downloadSpeed;
            set
            {
                _downloadSpeed = value;
                OnPropertyChanged(nameof(DownloadSpeed));
                OnPropertyChanged(nameof(DownloadSpeedFormatted));
            }
        }

        public double UploadSpeed
        {
            get => _uploadSpeed;
            set
            {
                _uploadSpeed = value;
                OnPropertyChanged(nameof(UploadSpeed));
                OnPropertyChanged(nameof(UploadSpeedFormatted));
            }
        }

        public long TotalDownloaded
        {
            get => _totalDownloaded;
            set
            {
                _totalDownloaded = value;
                OnPropertyChanged(nameof(TotalDownloaded));
                OnPropertyChanged(nameof(TotalDownloadedFormatted));
            }
        }

        public long TotalUploaded
        {
            get => _totalUploaded;
            set
            {
                _totalUploaded = value;
                OnPropertyChanged(nameof(TotalUploaded));
                OnPropertyChanged(nameof(TotalUploadedFormatted));
            }
        }

        // Formatted properties for display
        public string DownloadSpeedFormatted => FormatSpeed(DownloadSpeed);
        public string UploadSpeedFormatted => FormatSpeed(UploadSpeed);
        public string TotalDownloadedFormatted => FormatBytes(TotalDownloaded);
        public string TotalUploadedFormatted => FormatBytes(TotalUploaded);
        public string TotalDataFormatted => FormatBytes(TotalDownloaded + TotalUploaded);

        public double TotalSpeed => DownloadSpeed + UploadSpeed;
        public string TotalSpeedFormatted => FormatSpeed(TotalSpeed);

        private string FormatSpeed(double bytesPerSecond)
        {
            string[] suffixes = { "B/s", "KB/s", "MB/s", "GB/s" };
            int counter = 0;
            double number = bytesPerSecond;
            
            while (number >= 1024 && counter < suffixes.Length - 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:F2} {suffixes[counter]}";
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class NetworkInterface : INotifyPropertyChanged
    {
        private bool _isActive;
        private string _status;

        public string Name { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public long Speed { get; set; }
        public string MacAddress { get; set; }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        public string SpeedFormatted
        {
            get
            {
                if (Speed <= 0) return "Unknown";
                
                if (Speed >= 1_000_000_000)
                    return $"{Speed / 1_000_000_000:F1} Gbps";
                if (Speed >= 1_000_000)
                    return $"{Speed / 1_000_000:F0} Mbps";
                if (Speed >= 1_000)
                    return $"{Speed / 1_000:F0} Kbps";
                
                return $"{Speed} bps";
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ApplicationUsage : INotifyPropertyChanged
    {
        private long _bytesReceived;
        private long _bytesSent;
        private double _currentDownloadSpeed;
        private double _currentUploadSpeed;

        public int ProcessId { get; set; }
        public string ProcessName { get; set; }
        public string ProcessPath { get; set; }
        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }

        public long BytesReceived
        {
            get => _bytesReceived;
            set
            {
                _bytesReceived = value;
                OnPropertyChanged(nameof(BytesReceived));
                OnPropertyChanged(nameof(BytesReceivedFormatted));
                OnPropertyChanged(nameof(TotalBytesFormatted));
            }
        }

        public long BytesSent
        {
            get => _bytesSent;
            set
            {
                _bytesSent = value;
                OnPropertyChanged(nameof(BytesSent));
                OnPropertyChanged(nameof(BytesSentFormatted));
                OnPropertyChanged(nameof(TotalBytesFormatted));
            }
        }

        public double CurrentDownloadSpeed
        {
            get => _currentDownloadSpeed;
            set
            {
                _currentDownloadSpeed = value;
                OnPropertyChanged(nameof(CurrentDownloadSpeed));
                OnPropertyChanged(nameof(CurrentDownloadSpeedFormatted));
            }
        }

        public double CurrentUploadSpeed
        {
            get => _currentUploadSpeed;
            set
            {
                _currentUploadSpeed = value;
                OnPropertyChanged(nameof(CurrentUploadSpeed));
                OnPropertyChanged(nameof(CurrentUploadSpeedFormatted));
            }
        }

        // Formatted properties
        public string BytesReceivedFormatted => FormatBytes(BytesReceived);
        public string BytesSentFormatted => FormatBytes(BytesSent);
        public string TotalBytesFormatted => FormatBytes(BytesReceived + BytesSent);
        public string CurrentDownloadSpeedFormatted => FormatSpeed(CurrentDownloadSpeed);
        public string CurrentUploadSpeedFormatted => FormatSpeed(CurrentUploadSpeed);

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        private string FormatSpeed(double bytesPerSecond)
        {
            string[] suffixes = { "B/s", "KB/s", "MB/s", "GB/s" };
            int counter = 0;
            double number = bytesPerSecond;
            
            while (number >= 1024 && counter < suffixes.Length - 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:F2} {suffixes[counter]}";
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
