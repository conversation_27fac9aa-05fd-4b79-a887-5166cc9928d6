using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Threading;
using NetworkMonitor.Core;
using NetworkMonitor.Models;
using NetworkMonitor.Services;
using LiveCharts;
using LiveCharts.Wpf;
using LiveCharts.Configurations;

namespace NetworkMonitor.UI
{
    public partial class MainWindow : Window
    {
        private ConnectionMonitor _connectionMonitor;
        private BandwidthMonitor _bandwidthMonitor;
        private NotificationService _notificationService;
        private DispatcherTimer _uiUpdateTimer;
        private bool _isMonitoring;
        private readonly Dictionary<int, ApplicationUsage> _applicationUsage = new Dictionary<int, ApplicationUsage>();

        // Chart properties
        public SeriesCollection BandwidthSeries { get; set; }
        public SeriesCollection DetailedBandwidthSeries { get; set; }
        public Func<double, string> TimeFormatter { get; set; }
        public Func<double, string> SpeedFormatter { get; set; }

        public MainWindow()
        {
            InitializeComponent();
            InitializeCharts();
            InitializeMonitors();
            InitializeUI();
            SetupEventHandlers();
        }

        private void InitializeCharts()
        {
            // Configure chart mappers
            var mapper = Mappers.Xy<BandwidthData>()
                .X(model => model.Timestamp.Ticks)
                .Y(model => model.DownloadSpeed / (1024 * 1024)); // Convert to MB/s

            Charting.For<BandwidthData>(mapper);

            // Initialize formatters
            TimeFormatter = value => new DateTime((long)value).ToString("HH:mm:ss");
            SpeedFormatter = value => $"{value:F2} MB/s";

            // Initialize series for dashboard chart
            BandwidthSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "Download",
                    Values = new ChartValues<BandwidthData>(),
                    PointGeometry = null,
                    LineSmoothness = 0.3,
                    Stroke = System.Windows.Media.Brushes.Blue,
                    Fill = System.Windows.Media.Brushes.Transparent
                },
                new LineSeries
                {
                    Title = "Upload",
                    Values = new ChartValues<BandwidthData>(),
                    PointGeometry = null,
                    LineSmoothness = 0.3,
                    Stroke = System.Windows.Media.Brushes.Red,
                    Fill = System.Windows.Media.Brushes.Transparent
                }
            };

            // Initialize series for detailed chart
            DetailedBandwidthSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "Download Speed",
                    Values = new ChartValues<BandwidthData>(),
                    PointGeometry = DefaultGeometries.Circle,
                    PointGeometrySize = 3,
                    LineSmoothness = 0.3,
                    Stroke = System.Windows.Media.Brushes.Blue,
                    Fill = System.Windows.Media.Brushes.Transparent
                },
                new LineSeries
                {
                    Title = "Upload Speed",
                    Values = new ChartValues<BandwidthData>(),
                    PointGeometry = DefaultGeometries.Circle,
                    PointGeometrySize = 3,
                    LineSmoothness = 0.3,
                    Stroke = System.Windows.Media.Brushes.Red,
                    Fill = System.Windows.Media.Brushes.Transparent
                }
            };

            // Set chart data context
            BandwidthChart.Series = BandwidthSeries;
            DetailedBandwidthChart.Series = DetailedBandwidthSeries;

            // Set data context for formatters
            DataContext = this;
        }

        private void InitializeMonitors()
        {
            try
            {
                _connectionMonitor = new ConnectionMonitor();
                _bandwidthMonitor = new BandwidthMonitor();

                // Get notification service from App
                if (Application.Current is App app)
                {
                    _notificationService = new NotificationService();
                    _notificationService.ShowMainWindow += (s, e) => {
                        try
                        {
                            Show();
                            WindowState = WindowState.Normal;
                            Activate();
                        }
                        catch (Exception ex)
                        {
                            LogError("Error showing main window", ex);
                        }
                    };
                    _notificationService.ExitApplication += (s, e) => {
                        try
                        {
                            Application.Current.Shutdown();
                        }
                        catch (Exception ex)
                        {
                            LogError("Error shutting down application", ex);
                        }
                    };
                }

                // Setup UI update timer
                _uiUpdateTimer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(1)
                };
                _uiUpdateTimer.Tick += UpdateUI;

                LogInfo("Monitors initialized successfully");
            }
            catch (Exception ex)
            {
                LogError("Error initializing monitors", ex);
                MessageBox.Show($"Error initializing monitoring components: {ex.Message}",
                    "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeUI()
        {
            // Bind data sources
            ConnectionsGrid.ItemsSource = _connectionMonitor.Connections;
            BandwidthHistoryGrid.ItemsSource = _bandwidthMonitor.BandwidthHistory;
            InterfaceComboBox.ItemsSource = _bandwidthMonitor.NetworkInterfaces;

            // Set default interface
            if (_bandwidthMonitor.NetworkInterfaces.Count > 0)
            {
                InterfaceComboBox.SelectedIndex = 0;
                _bandwidthMonitor.SelectedInterface = _bandwidthMonitor.NetworkInterfaces[0];
            }

            // Initialize application usage grid
            ApplicationsGrid.ItemsSource = _applicationUsage.Values;
        }

        private void SetupEventHandlers()
        {
            _connectionMonitor.ConnectionAdded += OnConnectionAdded;
            _connectionMonitor.ConnectionRemoved += OnConnectionRemoved;
            _connectionMonitor.ConnectionUpdated += OnConnectionUpdated;

            _bandwidthMonitor.PropertyChanged += OnBandwidthMonitorPropertyChanged;

            // Handle window state changes for system tray
            StateChanged += OnWindowStateChanged;
            Closing += OnWindowClosing;
        }

        private void OnConnectionAdded(object sender, NetworkConnection connection)
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateApplicationUsage(connection);

                if (connection.IsSuspicious)
                {
                    _notificationService?.ShowSuspiciousConnectionAlert(
                        connection.ProcessName,
                        connection.RemoteEndpoint);
                }
            }));
        }

        private void OnConnectionRemoved(object sender, NetworkConnection connection)
        {
            // Connection removed - could log this
        }

        private void OnConnectionUpdated(object sender, NetworkConnection connection)
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateApplicationUsage(connection);
            }));
        }

        private void OnBandwidthMonitorPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(BandwidthMonitor.CurrentBandwidth))
            {
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    var current = _bandwidthMonitor.CurrentBandwidth;
                    if (current != null)
                    {
                        _notificationService?.UpdateIcon(current.DownloadSpeed, current.UploadSpeed);
                    }
                }));
            }
        }

        private void UpdateApplicationUsage(NetworkConnection connection)
        {
            if (!_applicationUsage.ContainsKey(connection.ProcessId))
            {
                _applicationUsage[connection.ProcessId] = new ApplicationUsage
                {
                    ProcessId = connection.ProcessId,
                    ProcessName = connection.ProcessName,
                    ProcessPath = connection.ProcessPath,
                    FirstSeen = connection.FirstSeen
                };
            }

            var usage = _applicationUsage[connection.ProcessId];
            usage.LastSeen = connection.LastSeen;
            usage.BytesReceived += connection.BytesReceived;
            usage.BytesSent += connection.BytesSent;
        }

        private void UpdateUI(object sender, EventArgs e)
        {
            try
            {
                UpdateDashboard();
                UpdateStatusBar();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error updating UI: {ex.Message}";
            }
        }

        private void UpdateDashboard()
        {
            var current = _bandwidthMonitor.CurrentBandwidth;
            if (current != null)
            {
                CurrentDownloadSpeed.Text = current.DownloadSpeedFormatted;
                CurrentUploadSpeed.Text = current.UploadSpeedFormatted;
                BandwidthDownloadSpeed.Text = current.DownloadSpeedFormatted;
                BandwidthUploadSpeed.Text = current.UploadSpeedFormatted;
                BandwidthTotalDownloaded.Text = current.TotalDownloadedFormatted;
                BandwidthTotalUploaded.Text = current.TotalUploadedFormatted;
            }

            // Update connection counts
            var connections = _connectionMonitor.Connections;
            TotalConnections.Text = connections.Count.ToString();
            EstablishedConnections.Text = connections.Count(c => c.State == "Established").ToString();

            // Update session data
            if (_bandwidthMonitor.BandwidthHistory.Count > 0)
            {
                var first = _bandwidthMonitor.BandwidthHistory.First();
                var last = _bandwidthMonitor.BandwidthHistory.Last();

                SessionDownloaded.Text = FormatBytes(last.TotalDownloaded - first.TotalDownloaded);
                SessionUploaded.Text = FormatBytes(last.TotalUploaded - first.TotalUploaded);
            }

            // Update peak speeds
            var peak24h = _bandwidthMonitor.GetPeakBandwidth(TimeSpan.FromHours(24));
            if (peak24h != null)
            {
                PeakDownloadSpeed.Text = peak24h.DownloadSpeedFormatted;
                PeakUploadSpeed.Text = peak24h.UploadSpeedFormatted;
            }

            // Update top applications
            var topApps = _applicationUsage.Values
                .OrderByDescending(a => a.BytesReceived + a.BytesSent)
                .Take(10)
                .ToList();
            TopApplicationsGrid.ItemsSource = topApps;

            // Update charts
            UpdateCharts();
        }

        private void UpdateCharts()
        {
            try
            {
                var bandwidthHistory = _bandwidthMonitor.BandwidthHistory;
                if (bandwidthHistory.Count == 0) return;

                // Limit chart data to last 100 points for performance
                var recentData = bandwidthHistory.TakeLast(100).ToList();

                // Update dashboard chart (simplified view)
                var dashboardData = recentData.TakeLast(20).ToList();

                if (BandwidthSeries.Count >= 2)
                {
                    var downloadSeries = BandwidthSeries[0].Values as ChartValues<BandwidthData>;
                    var uploadSeries = BandwidthSeries[1].Values as ChartValues<BandwidthData>;

                    downloadSeries?.Clear();
                    uploadSeries?.Clear();

                    foreach (var data in dashboardData)
                    {
                        downloadSeries?.Add(data);
                        uploadSeries?.Add(data);
                    }
                }

                // Update detailed chart
                if (DetailedBandwidthSeries.Count >= 2)
                {
                    var detailedDownloadSeries = DetailedBandwidthSeries[0].Values as ChartValues<BandwidthData>;
                    var detailedUploadSeries = DetailedBandwidthSeries[1].Values as ChartValues<BandwidthData>;

                    detailedDownloadSeries?.Clear();
                    detailedUploadSeries?.Clear();

                    foreach (var data in recentData)
                    {
                        detailedDownloadSeries?.Add(data);
                        detailedUploadSeries?.Add(data);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating charts: {ex.Message}");
            }
        }

        private void UpdateStatusBar()
        {
            ConnectionCountStatus.Text = $"Connections: {_connectionMonitor.Connections.Count}";
            MonitoringStatus.Text = $"Monitoring: {(_isMonitoring ? "Running" : "Stopped")}";
            LastUpdateStatus.Text = $"Last Update: {DateTime.Now:HH:mm:ss}";

            if (_isMonitoring)
            {
                StatusText.Text = "Monitoring network activity...";
            }
            else
            {
                StatusText.Text = "Ready";
            }
        }

        private void ApplyFilters()
        {
            var view = CollectionViewSource.GetDefaultView(ConnectionsGrid.ItemsSource);
            if (view != null)
            {
                view.Filter = FilterConnections;
            }
        }

        private bool FilterConnections(object item)
        {
            if (!(item is NetworkConnection connection)) return false;

            // Protocol filter
            var protocolFilter = (ComboBoxItem)ProtocolFilter.SelectedItem;
            if (protocolFilter?.Content.ToString() != "All")
            {
                if (!connection.Protocol.StartsWith(protocolFilter.Content.ToString()))
                    return false;
            }

            // State filter
            var stateFilter = (ComboBoxItem)StateFilter.SelectedItem;
            if (stateFilter?.Content.ToString() != "All")
            {
                if (connection.State != stateFilter.Content.ToString())
                    return false;
            }

            // Suspicious filter
            if (ShowSuspiciousOnly.IsChecked == true)
            {
                if (!connection.IsSuspicious)
                    return false;
            }

            return true;
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{number:n1} {suffixes[counter]}";
        }

        // Event Handlers
        private void StartStop_Click(object sender, RoutedEventArgs e)
        {
            if (_isMonitoring)
            {
                StopMonitoring();
            }
            else
            {
                StartMonitoring();
            }
        }

        private void StartMonitoring()
        {
            try
            {
                LogInfo("Starting network monitoring");

                _connectionMonitor.Start();
                _bandwidthMonitor.Start();
                _uiUpdateTimer.Start();

                _isMonitoring = true;
                StartStopText.Text = "Stop Monitoring";
                StatusText.Text = "Network monitoring started successfully";

                LogInfo("Network monitoring started successfully");
            }
            catch (Exception ex)
            {
                LogError("Error starting monitoring", ex);
                StatusText.Text = "Failed to start monitoring";
                MessageBox.Show($"Error starting monitoring: {ex.Message}\n\nPlease check that you have administrator privileges and try again.",
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void StopMonitoring()
        {
            try
            {
                LogInfo("Stopping network monitoring");

                _connectionMonitor.Stop();
                _bandwidthMonitor.Stop();
                _uiUpdateTimer.Stop();

                _isMonitoring = false;
                StartStopText.Text = "Start Monitoring";
                StatusText.Text = "Monitoring stopped";

                LogInfo("Network monitoring stopped successfully");
            }
            catch (Exception ex)
            {
                LogError("Error stopping monitoring", ex);
                StatusText.Text = "Error stopping monitoring";
                MessageBox.Show($"Error stopping monitoring: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateInterval_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (UpdateIntervalComboBox.SelectedItem is ComboBoxItem item &&
                int.TryParse(item.Tag.ToString(), out int interval))
            {
                _connectionMonitor.UpdateInterval = interval;
                _bandwidthMonitor.UpdateInterval = interval;
            }
        }

        private void Interface_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (InterfaceComboBox.SelectedItem is NetworkInterface selectedInterface)
            {
                _bandwidthMonitor.SelectedInterface = selectedInterface;
            }
        }

        private void Filter_Changed(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void Filter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ExportData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|JSON files (*.json)|*.json|All files (*.*)|*.*",
                    DefaultExt = "csv"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var filePath = saveDialog.FileName;
                    var extension = System.IO.Path.GetExtension(filePath).ToLower();

                    StatusText.Text = "Exporting data...";

                    switch (extension)
                    {
                        case ".csv":
                            ExportToCsv(filePath);
                            break;
                        case ".json":
                            ExportToJson(filePath);
                            break;
                        default:
                            MessageBox.Show("Unsupported file format. Please use .csv or .json extension.",
                                "Export Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                            return;
                    }

                    StatusText.Text = "Data exported successfully";
                    MessageBox.Show($"Data exported successfully to:\n{filePath}",
                        "Export Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                StatusText.Text = "Export failed";
                MessageBox.Show($"Error exporting data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Force refresh of all data
                if (_isMonitoring)
                {
                    StopMonitoring();
                    StartMonitoring();
                }
                StatusText.Text = "Data refreshed";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearHistory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("Are you sure you want to clear all history data?",
                    "Clear History", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _bandwidthMonitor.BandwidthHistory.Clear();
                    _applicationUsage.Clear();
                    StatusText.Text = "History cleared";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error clearing history: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AlwaysOnTop_Click(object sender, RoutedEventArgs e)
        {
            if (sender is MenuItem menuItem)
            {
                Topmost = menuItem.IsChecked;
            }
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsWindow = new SettingsWindow
                {
                    Owner = this
                };

                if (settingsWindow.ShowDialog() == true)
                {
                    // Settings were saved, apply any immediate changes
                    ApplySettingsChanges();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening settings: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplySettingsChanges()
        {
            try
            {
                var settingsService = new Services.SettingsService();
                var settings = settingsService.GetCurrentSettings();

                // Apply immediate settings changes
                Topmost = settings.AlwaysOnTop;

                // Update monitoring intervals
                _connectionMonitor.UpdateInterval = settings.UpdateInterval;
                _bandwidthMonitor.UpdateInterval = settings.UpdateInterval;

                StatusText.Text = "Settings applied successfully";
            }
            catch (Exception ex)
            {
                StatusText.Text = "Error applying settings";
                System.Diagnostics.Debug.WriteLine($"Error applying settings: {ex.Message}");
            }
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show(
                "Network Monitor v1.0\n\n" +
                "A comprehensive Windows network monitoring application.\n\n" +
                "Features:\n" +
                "• Real-time connection monitoring\n" +
                "• Bandwidth tracking\n" +
                "• Per-application usage\n" +
                "• Security monitoring\n\n" +
                "Copyright © 2024",
                "About Network Monitor",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private void OnWindowStateChanged(object sender, EventArgs e)
        {
            if (WindowState == WindowState.Minimized)
            {
                Hide();
                _notificationService?.ShowBalloonTip(
                    "Network Monitor",
                    "Application minimized to system tray");
            }
        }

        private void OnWindowClosing(object sender, CancelEventArgs e)
        {
            // Minimize to tray instead of closing
            e.Cancel = true;
            WindowState = WindowState.Minimized;
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                StopMonitoring();
                _connectionMonitor?.Dispose();
                _bandwidthMonitor?.Dispose();
                _notificationService?.Dispose();
                _uiUpdateTimer?.Stop();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during cleanup: {ex.Message}");
            }

            base.OnClosed(e);
        }

        private void ExportToCsv(string filePath)
        {
            using var writer = new System.IO.StreamWriter(filePath, false, System.Text.Encoding.UTF8);

            // Export connections data
            writer.WriteLine("=== Network Connections ===");
            writer.WriteLine("ProcessName,ProcessId,Protocol,LocalEndpoint,RemoteEndpoint,State,BytesReceived,BytesSent,FirstSeen,LastSeen");

            foreach (var connection in _connectionMonitor.Connections)
            {
                writer.WriteLine($"\"{connection.ProcessName}\",{connection.ProcessId},\"{connection.Protocol}\"," +
                    $"\"{connection.LocalEndpoint}\",\"{connection.RemoteEndpoint}\",\"{connection.State}\"," +
                    $"{connection.BytesReceived},{connection.BytesSent}," +
                    $"\"{connection.FirstSeen:yyyy-MM-dd HH:mm:ss}\",\"{connection.LastSeen:yyyy-MM-dd HH:mm:ss}\"");
            }

            writer.WriteLine();
            writer.WriteLine("=== Bandwidth History ===");
            writer.WriteLine("Timestamp,DownloadSpeed,UploadSpeed,TotalDownloaded,TotalUploaded");

            foreach (var bandwidth in _bandwidthMonitor.BandwidthHistory)
            {
                writer.WriteLine($"\"{bandwidth.Timestamp:yyyy-MM-dd HH:mm:ss}\"," +
                    $"{bandwidth.DownloadSpeed},{bandwidth.UploadSpeed}," +
                    $"{bandwidth.TotalDownloaded},{bandwidth.TotalUploaded}");
            }

            writer.WriteLine();
            writer.WriteLine("=== Application Usage ===");
            writer.WriteLine("ProcessName,ProcessId,ProcessPath,BytesReceived,BytesSent,FirstSeen,LastSeen");

            foreach (var app in _applicationUsage.Values)
            {
                writer.WriteLine($"\"{app.ProcessName}\",{app.ProcessId},\"{app.ProcessPath}\"," +
                    $"{app.BytesReceived},{app.BytesSent}," +
                    $"\"{app.FirstSeen:yyyy-MM-dd HH:mm:ss}\",\"{app.LastSeen:yyyy-MM-dd HH:mm:ss}\"");
            }
        }

        private void ExportToJson(string filePath)
        {
            var exportData = new
            {
                ExportTimestamp = DateTime.Now,
                NetworkConnections = _connectionMonitor.Connections.ToList(),
                BandwidthHistory = _bandwidthMonitor.BandwidthHistory.ToList(),
                ApplicationUsage = _applicationUsage.Values.ToList()
            };

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(exportData, Newtonsoft.Json.Formatting.Indented);
            System.IO.File.WriteAllText(filePath, json, System.Text.Encoding.UTF8);
        }

        // Logging helper methods
        private void LogInfo(string message)
        {
            if (Application.Current is App app && app.LoggingService != null)
            {
                app.LoggingService.LogInfo(message);
            }
        }

        private void LogError(string message, Exception exception = null)
        {
            if (Application.Current is App app && app.LoggingService != null)
            {
                app.LoggingService.LogError(message, exception);
            }
        }

        private void LogWarning(string message)
        {
            if (Application.Current is App app && app.LoggingService != null)
            {
                app.LoggingService.LogWarning(message);
            }
        }

        private void LogDebug(string message)
        {
            if (Application.Current is App app && app.LoggingService != null)
            {
                app.LoggingService.LogDebug(message);
            }
        }
    }
}
