using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Threading;
using NetworkMonitor.Core;
using NetworkMonitor.Models;
using NetworkMonitor.Services;

namespace NetworkMonitor.UI
{
    public partial class MainWindow : Window
    {
        private ConnectionMonitor _connectionMonitor;
        private BandwidthMonitor _bandwidthMonitor;
        private NotificationService _notificationService;
        private DispatcherTimer _uiUpdateTimer;
        private bool _isMonitoring;
        private readonly Dictionary<int, ApplicationUsage> _applicationUsage = new Dictionary<int, ApplicationUsage>();

        public MainWindow()
        {
            InitializeComponent();
            InitializeMonitors();
            InitializeUI();
            SetupEventHandlers();
        }

        private void InitializeMonitors()
        {
            _connectionMonitor = new ConnectionMonitor();
            _bandwidthMonitor = new BandwidthMonitor();

            // Get notification service from App
            if (Application.Current is App app)
            {
                _notificationService = new NotificationService();
                _notificationService.ShowMainWindow += (s, e) => {
                    Show();
                    WindowState = WindowState.Normal;
                    Activate();
                };
                _notificationService.ExitApplication += (s, e) => Application.Current.Shutdown();
            }

            // Setup UI update timer
            _uiUpdateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _uiUpdateTimer.Tick += UpdateUI;
        }

        private void InitializeUI()
        {
            // Bind data sources
            ConnectionsGrid.ItemsSource = _connectionMonitor.Connections;
            BandwidthHistoryGrid.ItemsSource = _bandwidthMonitor.BandwidthHistory;
            InterfaceComboBox.ItemsSource = _bandwidthMonitor.NetworkInterfaces;

            // Set default interface
            if (_bandwidthMonitor.NetworkInterfaces.Count > 0)
            {
                InterfaceComboBox.SelectedIndex = 0;
                _bandwidthMonitor.SelectedInterface = _bandwidthMonitor.NetworkInterfaces[0];
            }

            // Initialize application usage grid
            ApplicationsGrid.ItemsSource = _applicationUsage.Values;
        }

        private void SetupEventHandlers()
        {
            _connectionMonitor.ConnectionAdded += OnConnectionAdded;
            _connectionMonitor.ConnectionRemoved += OnConnectionRemoved;
            _connectionMonitor.ConnectionUpdated += OnConnectionUpdated;

            _bandwidthMonitor.PropertyChanged += OnBandwidthMonitorPropertyChanged;

            // Handle window state changes for system tray
            StateChanged += OnWindowStateChanged;
            Closing += OnWindowClosing;
        }

        private void OnConnectionAdded(object sender, NetworkConnection connection)
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateApplicationUsage(connection);

                if (connection.IsSuspicious)
                {
                    _notificationService?.ShowSuspiciousConnectionAlert(
                        connection.ProcessName,
                        connection.RemoteEndpoint);
                }
            }));
        }

        private void OnConnectionRemoved(object sender, NetworkConnection connection)
        {
            // Connection removed - could log this
        }

        private void OnConnectionUpdated(object sender, NetworkConnection connection)
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateApplicationUsage(connection);
            }));
        }

        private void OnBandwidthMonitorPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(BandwidthMonitor.CurrentBandwidth))
            {
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    var current = _bandwidthMonitor.CurrentBandwidth;
                    if (current != null)
                    {
                        _notificationService?.UpdateIcon(current.DownloadSpeed, current.UploadSpeed);
                    }
                }));
            }
        }

        private void UpdateApplicationUsage(NetworkConnection connection)
        {
            if (!_applicationUsage.ContainsKey(connection.ProcessId))
            {
                _applicationUsage[connection.ProcessId] = new ApplicationUsage
                {
                    ProcessId = connection.ProcessId,
                    ProcessName = connection.ProcessName,
                    ProcessPath = connection.ProcessPath,
                    FirstSeen = connection.FirstSeen
                };
            }

            var usage = _applicationUsage[connection.ProcessId];
            usage.LastSeen = connection.LastSeen;
            usage.BytesReceived += connection.BytesReceived;
            usage.BytesSent += connection.BytesSent;
        }

        private void UpdateUI(object sender, EventArgs e)
        {
            try
            {
                UpdateDashboard();
                UpdateStatusBar();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Error updating UI: {ex.Message}";
            }
        }

        private void UpdateDashboard()
        {
            var current = _bandwidthMonitor.CurrentBandwidth;
            if (current != null)
            {
                CurrentDownloadSpeed.Text = current.DownloadSpeedFormatted;
                CurrentUploadSpeed.Text = current.UploadSpeedFormatted;
                BandwidthDownloadSpeed.Text = current.DownloadSpeedFormatted;
                BandwidthUploadSpeed.Text = current.UploadSpeedFormatted;
                BandwidthTotalDownloaded.Text = current.TotalDownloadedFormatted;
                BandwidthTotalUploaded.Text = current.TotalUploadedFormatted;
            }

            // Update connection counts
            var connections = _connectionMonitor.Connections;
            TotalConnections.Text = connections.Count.ToString();
            EstablishedConnections.Text = connections.Count(c => c.State == "Established").ToString();

            // Update session data
            if (_bandwidthMonitor.BandwidthHistory.Count > 0)
            {
                var first = _bandwidthMonitor.BandwidthHistory.First();
                var last = _bandwidthMonitor.BandwidthHistory.Last();

                SessionDownloaded.Text = FormatBytes(last.TotalDownloaded - first.TotalDownloaded);
                SessionUploaded.Text = FormatBytes(last.TotalUploaded - first.TotalUploaded);
            }

            // Update peak speeds
            var peak24h = _bandwidthMonitor.GetPeakBandwidth(TimeSpan.FromHours(24));
            if (peak24h != null)
            {
                PeakDownloadSpeed.Text = peak24h.DownloadSpeedFormatted;
                PeakUploadSpeed.Text = peak24h.UploadSpeedFormatted;
            }

            // Update top applications
            var topApps = _applicationUsage.Values
                .OrderByDescending(a => a.BytesReceived + a.BytesSent)
                .Take(10)
                .ToList();
            TopApplicationsGrid.ItemsSource = topApps;
        }

        private void UpdateStatusBar()
        {
            ConnectionCountStatus.Text = $"Connections: {_connectionMonitor.Connections.Count}";
            MonitoringStatus.Text = $"Monitoring: {(_isMonitoring ? "Running" : "Stopped")}";
            LastUpdateStatus.Text = $"Last Update: {DateTime.Now:HH:mm:ss}";

            if (_isMonitoring)
            {
                StatusText.Text = "Monitoring network activity...";
            }
            else
            {
                StatusText.Text = "Ready";
            }
        }

        private void ApplyFilters()
        {
            var view = CollectionViewSource.GetDefaultView(ConnectionsGrid.ItemsSource);
            if (view != null)
            {
                view.Filter = FilterConnections;
            }
        }

        private bool FilterConnections(object item)
        {
            if (!(item is NetworkConnection connection)) return false;

            // Protocol filter
            var protocolFilter = (ComboBoxItem)ProtocolFilter.SelectedItem;
            if (protocolFilter?.Content.ToString() != "All")
            {
                if (!connection.Protocol.StartsWith(protocolFilter.Content.ToString()))
                    return false;
            }

            // State filter
            var stateFilter = (ComboBoxItem)StateFilter.SelectedItem;
            if (stateFilter?.Content.ToString() != "All")
            {
                if (connection.State != stateFilter.Content.ToString())
                    return false;
            }

            // Suspicious filter
            if (ShowSuspiciousOnly.IsChecked == true)
            {
                if (!connection.IsSuspicious)
                    return false;
            }

            return true;
        }

        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{number:n1} {suffixes[counter]}";
        }

        // Event Handlers
        private void StartStop_Click(object sender, RoutedEventArgs e)
        {
            if (_isMonitoring)
            {
                StopMonitoring();
            }
            else
            {
                StartMonitoring();
            }
        }

        private void StartMonitoring()
        {
            try
            {
                _connectionMonitor.Start();
                _bandwidthMonitor.Start();
                _uiUpdateTimer.Start();

                _isMonitoring = true;
                StartStopText.Text = "Stop Monitoring";
                StatusText.Text = "Starting network monitoring...";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error starting monitoring: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void StopMonitoring()
        {
            try
            {
                _connectionMonitor.Stop();
                _bandwidthMonitor.Stop();
                _uiUpdateTimer.Stop();

                _isMonitoring = false;
                StartStopText.Text = "Start Monitoring";
                StatusText.Text = "Monitoring stopped";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error stopping monitoring: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateInterval_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (UpdateIntervalComboBox.SelectedItem is ComboBoxItem item &&
                int.TryParse(item.Tag.ToString(), out int interval))
            {
                _connectionMonitor.UpdateInterval = interval;
                _bandwidthMonitor.UpdateInterval = interval;
            }
        }

        private void Interface_Changed(object sender, SelectionChangedEventArgs e)
        {
            if (InterfaceComboBox.SelectedItem is NetworkInterface selectedInterface)
            {
                _bandwidthMonitor.SelectedInterface = selectedInterface;
            }
        }

        private void Filter_Changed(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void Filter_Changed(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ExportData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv|JSON files (*.json)|*.json|All files (*.*)|*.*",
                    DefaultExt = "csv"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    // TODO: Implement data export
                    MessageBox.Show("Data export functionality will be implemented here.",
                        "Export Data", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error exporting data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Force refresh of all data
                if (_isMonitoring)
                {
                    StopMonitoring();
                    StartMonitoring();
                }
                StatusText.Text = "Data refreshed";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearHistory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("Are you sure you want to clear all history data?",
                    "Clear History", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _bandwidthMonitor.BandwidthHistory.Clear();
                    _applicationUsage.Clear();
                    StatusText.Text = "History cleared";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error clearing history: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AlwaysOnTop_Click(object sender, RoutedEventArgs e)
        {
            if (sender is MenuItem menuItem)
            {
                Topmost = menuItem.IsChecked;
            }
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Settings window will be implemented here.",
                "Settings", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show(
                "Network Monitor v1.0\n\n" +
                "A comprehensive Windows network monitoring application.\n\n" +
                "Features:\n" +
                "• Real-time connection monitoring\n" +
                "• Bandwidth tracking\n" +
                "• Per-application usage\n" +
                "• Security monitoring\n\n" +
                "Copyright © 2024",
                "About Network Monitor",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private void OnWindowStateChanged(object sender, EventArgs e)
        {
            if (WindowState == WindowState.Minimized)
            {
                Hide();
                _notificationService?.ShowBalloonTip(
                    "Network Monitor",
                    "Application minimized to system tray");
            }
        }

        private void OnWindowClosing(object sender, CancelEventArgs e)
        {
            // Minimize to tray instead of closing
            e.Cancel = true;
            WindowState = WindowState.Minimized;
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                StopMonitoring();
                _connectionMonitor?.Dispose();
                _bandwidthMonitor?.Dispose();
                _notificationService?.Dispose();
                _uiUpdateTimer?.Stop();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during cleanup: {ex.Message}");
            }

            base.OnClosed(e);
        }
    }
}
