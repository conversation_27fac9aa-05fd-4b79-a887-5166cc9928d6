using System;
using System.Drawing;
using System.Windows.Forms;
using System.ComponentModel;

namespace NetworkMonitor.Services
{
    public class NotificationService : IDisposable
    {
        private NotifyIcon _notifyIcon;
        private ContextMenuStrip _contextMenu;
        private bool _disposed;

        public event EventHandler ShowMainWindow;
        public event EventHandler ExitApplication;

        public NotificationService()
        {
            InitializeNotifyIcon();
        }

        private void InitializeNotifyIcon()
        {
            try
            {
                // Create context menu
                _contextMenu = new ContextMenuStrip();
                
                var showItem = new ToolStripMenuItem("Show Network Monitor");
                showItem.Click += (s, e) => ShowMainWindow?.Invoke(this, EventArgs.Empty);
                showItem.Font = new Font(showItem.Font, FontStyle.Bold);
                
                var separatorItem = new ToolStripSeparator();
                
                var exitItem = new ToolStripMenuItem("Exit");
                exitItem.Click += (s, e) => ExitApplication?.Invoke(this, EventArgs.Empty);
                
                _contextMenu.Items.AddRange(new ToolStripItem[] { showItem, separatorItem, exitItem });

                // Create notify icon
                _notifyIcon = new NotifyIcon
                {
                    Icon = CreateDefaultIcon(),
                    Text = "Network Monitor",
                    ContextMenuStrip = _contextMenu,
                    Visible = true
                };

                _notifyIcon.DoubleClick += (s, e) => ShowMainWindow?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing notification service: {ex.Message}");
            }
        }

        private Icon CreateDefaultIcon()
        {
            try
            {
                // Create a simple default icon
                var bitmap = new Bitmap(16, 16);
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.Clear(Color.Transparent);
                    graphics.FillEllipse(Brushes.Blue, 2, 2, 12, 12);
                    graphics.DrawEllipse(Pens.White, 2, 2, 12, 12);
                }
                
                return Icon.FromHandle(bitmap.GetHicon());
            }
            catch
            {
                // Return system default icon if creation fails
                return SystemIcons.Information;
            }
        }

        public void UpdateIcon(double downloadSpeed, double uploadSpeed)
        {
            if (_disposed || _notifyIcon == null) return;

            try
            {
                var totalSpeed = downloadSpeed + uploadSpeed;
                var speedText = FormatSpeed(totalSpeed);
                
                _notifyIcon.Text = $"Network Monitor\nSpeed: {speedText}";
                
                // Update icon color based on activity
                var icon = CreateActivityIcon(totalSpeed);
                _notifyIcon.Icon = icon;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating notification icon: {ex.Message}");
            }
        }

        private Icon CreateActivityIcon(double totalSpeed)
        {
            try
            {
                var bitmap = new Bitmap(16, 16);
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.Clear(Color.Transparent);
                    
                    // Choose color based on activity level
                    Brush brush;
                    if (totalSpeed > 1024 * 1024) // > 1 MB/s
                        brush = Brushes.Red;
                    else if (totalSpeed > 1024 * 100) // > 100 KB/s
                        brush = Brushes.Orange;
                    else if (totalSpeed > 1024) // > 1 KB/s
                        brush = Brushes.Yellow;
                    else
                        brush = Brushes.Green;
                    
                    graphics.FillEllipse(brush, 2, 2, 12, 12);
                    graphics.DrawEllipse(Pens.White, 2, 2, 12, 12);
                    
                    // Add activity indicator
                    if (totalSpeed > 0)
                    {
                        graphics.FillEllipse(Brushes.White, 6, 6, 4, 4);
                    }
                }
                
                return Icon.FromHandle(bitmap.GetHicon());
            }
            catch
            {
                return CreateDefaultIcon();
            }
        }

        public void ShowBalloonTip(string title, string text, ToolTipIcon icon = ToolTipIcon.Info, int timeout = 3000)
        {
            if (_disposed || _notifyIcon == null) return;

            try
            {
                _notifyIcon.ShowBalloonTip(timeout, title, text, icon);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing balloon tip: {ex.Message}");
            }
        }

        public void ShowSuspiciousConnectionAlert(string processName, string remoteAddress)
        {
            ShowBalloonTip(
                "Suspicious Connection Detected",
                $"{processName} connected to {remoteAddress}",
                ToolTipIcon.Warning,
                5000);
        }

        public void ShowHighBandwidthAlert(string processName, double speed)
        {
            var speedText = FormatSpeed(speed);
            ShowBalloonTip(
                "High Bandwidth Usage",
                $"{processName} is using {speedText}",
                ToolTipIcon.Warning,
                5000);
        }

        public void ShowConnectionAlert(string message)
        {
            ShowBalloonTip(
                "Network Activity",
                message,
                ToolTipIcon.Info,
                3000);
        }

        private string FormatSpeed(double bytesPerSecond)
        {
            string[] suffixes = { "B/s", "KB/s", "MB/s", "GB/s" };
            int counter = 0;
            double number = bytesPerSecond;
            
            while (number >= 1024 && counter < suffixes.Length - 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:F1} {suffixes[counter]}";
        }

        public void Hide()
        {
            if (_notifyIcon != null)
                _notifyIcon.Visible = false;
        }

        public void Show()
        {
            if (_notifyIcon != null)
                _notifyIcon.Visible = true;
        }

        public void Dispose()
        {
            if (_disposed) return;

            try
            {
                _notifyIcon?.Dispose();
                _contextMenu?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing notification service: {ex.Message}");
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
