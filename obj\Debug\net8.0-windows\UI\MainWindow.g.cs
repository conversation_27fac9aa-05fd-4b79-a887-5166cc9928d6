﻿#pragma checksum "..\..\..\..\UI\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C9ADE17FA8ABF3A891FC1DE492B5795AEFC81D64"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace NetworkMonitor.UI {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartStopButton;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StartStopText;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UpdateIntervalComboBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox InterfaceComboBox;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl MainTabControl;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentDownloadSpeed;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentUploadSpeed;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalConnections;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EstablishedConnections;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionDownloaded;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionUploaded;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PeakDownloadSpeed;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PeakUploadSpeed;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart BandwidthChart;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid TopApplicationsGrid;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProtocolFilter;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StateFilter;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowSuspiciousOnly;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ConnectionsGrid;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BandwidthDownloadSpeed;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BandwidthUploadSpeed;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BandwidthTotalDownloaded;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BandwidthTotalUploaded;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart DetailedBandwidthChart;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid BandwidthHistoryGrid;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ApplicationsGrid;
        
        #line default
        #line hidden
        
        
        #line 333 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionCountStatus;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonitoringStatus;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\UI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateStatus;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/NetworkMonitor;V1.0.0.0;component/ui/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.16.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 45 "..\..\..\..\UI\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportData_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 47 "..\..\..\..\UI\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Exit_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 50 "..\..\..\..\UI\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Refresh_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 51 "..\..\..\..\UI\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearHistory_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 53 "..\..\..\..\UI\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.AlwaysOnTop_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 56 "..\..\..\..\UI\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Settings_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 57 "..\..\..\..\UI\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.About_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.StartStopButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\UI\MainWindow.xaml"
            this.StartStopButton.Click += new System.Windows.RoutedEventHandler(this.StartStop_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StartStopText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.UpdateIntervalComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 70 "..\..\..\..\UI\MainWindow.xaml"
            this.UpdateIntervalComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.UpdateInterval_Changed);
            
            #line default
            #line hidden
            return;
            case 11:
            this.InterfaceComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 78 "..\..\..\..\UI\MainWindow.xaml"
            this.InterfaceComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Interface_Changed);
            
            #line default
            #line hidden
            return;
            case 12:
            this.MainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 13:
            this.CurrentDownloadSpeed = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.CurrentUploadSpeed = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TotalConnections = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.EstablishedConnections = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.SessionDownloaded = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.SessionUploaded = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.PeakDownloadSpeed = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.PeakUploadSpeed = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.BandwidthChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 22:
            this.TopApplicationsGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 23:
            this.ProtocolFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 194 "..\..\..\..\UI\MainWindow.xaml"
            this.ProtocolFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Filter_Changed);
            
            #line default
            #line hidden
            return;
            case 24:
            this.StateFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 200 "..\..\..\..\UI\MainWindow.xaml"
            this.StateFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Filter_Changed);
            
            #line default
            #line hidden
            return;
            case 25:
            this.ShowSuspiciousOnly = ((System.Windows.Controls.CheckBox)(target));
            
            #line 207 "..\..\..\..\UI\MainWindow.xaml"
            this.ShowSuspiciousOnly.Checked += new System.Windows.RoutedEventHandler(this.Filter_Changed);
            
            #line default
            #line hidden
            
            #line 207 "..\..\..\..\UI\MainWindow.xaml"
            this.ShowSuspiciousOnly.Unchecked += new System.Windows.RoutedEventHandler(this.Filter_Changed);
            
            #line default
            #line hidden
            return;
            case 26:
            this.ConnectionsGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 27:
            this.BandwidthDownloadSpeed = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.BandwidthUploadSpeed = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.BandwidthTotalDownloaded = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.BandwidthTotalUploaded = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.DetailedBandwidthChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 32:
            this.BandwidthHistoryGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 33:
            this.ApplicationsGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 34:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.ConnectionCountStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.MonitoringStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.LastUpdateStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

