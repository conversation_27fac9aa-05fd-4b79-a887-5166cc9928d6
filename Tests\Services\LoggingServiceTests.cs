using System;
using System.IO;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using NetworkMonitor.Services;

namespace NetworkMonitor.Tests.Services
{
    [TestClass]
    public class LoggingServiceTests
    {
        private LoggingService _loggingService;
        private string _testLogDirectory;

        [TestInitialize]
        public void Setup()
        {
            _testLogDirectory = Path.Combine(Path.GetTempPath(), "NetworkMonitorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testLogDirectory);
            
            // Create a test logging service that uses our test directory
            _loggingService = new LoggingService();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _loggingService?.Dispose();
            
            if (Directory.Exists(_testLogDirectory))
            {
                Directory.Delete(_testLogDirectory, true);
            }
        }

        [TestMethod]
        public void LogInfo_ShouldCreateLogEntry()
        {
            // Arrange
            var testMessage = "Test info message";

            // Act
            _loggingService.LogInfo(testMessage);

            // Assert
            var logDirectory = _loggingService.GetLogDirectory();
            Assert.IsTrue(Directory.Exists(logDirectory));
            
            var logFiles = Directory.GetFiles(logDirectory, "*.log");
            Assert.IsTrue(logFiles.Length > 0);
        }

        [TestMethod]
        public void LogError_WithException_ShouldIncludeExceptionDetails()
        {
            // Arrange
            var testMessage = "Test error message";
            var testException = new InvalidOperationException("Test exception");

            // Act
            _loggingService.LogError(testMessage, testException);

            // Assert
            var logDirectory = _loggingService.GetLogDirectory();
            var logFiles = Directory.GetFiles(logDirectory, "*.log");
            Assert.IsTrue(logFiles.Length > 0);

            var logContent = File.ReadAllText(logFiles[0]);
            Assert.IsTrue(logContent.Contains(testMessage));
            Assert.IsTrue(logContent.Contains("InvalidOperationException"));
            Assert.IsTrue(logContent.Contains("Test exception"));
        }

        [TestMethod]
        public void LogDebug_ShouldCreateLogEntry()
        {
            // Arrange
            var testMessage = "Test debug message";

            // Act
            _loggingService.LogDebug(testMessage);

            // Assert
            var logDirectory = _loggingService.GetLogDirectory();
            var logFiles = Directory.GetFiles(logDirectory, "*.log");
            Assert.IsTrue(logFiles.Length > 0);

            var logContent = File.ReadAllText(logFiles[0]);
            Assert.IsTrue(logContent.Contains(testMessage));
            Assert.IsTrue(logContent.Contains("[Debug]"));
        }

        [TestMethod]
        public void LogWarning_ShouldCreateLogEntry()
        {
            // Arrange
            var testMessage = "Test warning message";

            // Act
            _loggingService.LogWarning(testMessage);

            // Assert
            var logDirectory = _loggingService.GetLogDirectory();
            var logFiles = Directory.GetFiles(logDirectory, "*.log");
            Assert.IsTrue(logFiles.Length > 0);

            var logContent = File.ReadAllText(logFiles[0]);
            Assert.IsTrue(logContent.Contains(testMessage));
            Assert.IsTrue(logContent.Contains("[Warning]"));
        }

        [TestMethod]
        public void LogCritical_ShouldCreateLogEntry()
        {
            // Arrange
            var testMessage = "Test critical message";

            // Act
            _loggingService.LogCritical(testMessage);

            // Assert
            var logDirectory = _loggingService.GetLogDirectory();
            var logFiles = Directory.GetFiles(logDirectory, "*.log");
            Assert.IsTrue(logFiles.Length > 0);

            var logContent = File.ReadAllText(logFiles[0]);
            Assert.IsTrue(logContent.Contains(testMessage));
            Assert.IsTrue(logContent.Contains("[Critical]"));
        }

        [TestMethod]
        public void Dispose_ShouldNotThrowException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _loggingService.Dispose());
        }

        [TestMethod]
        public void GetLogDirectory_ShouldReturnValidPath()
        {
            // Act
            var logDirectory = _loggingService.GetLogDirectory();

            // Assert
            Assert.IsFalse(string.IsNullOrEmpty(logDirectory));
            Assert.IsTrue(Directory.Exists(logDirectory));
        }
    }

    // Extension method for Assert.DoesNotThrow (not available in MSTest by default)
    public static class AssertExtensions
    {
        public static void DoesNotThrow(Action action)
        {
            try
            {
                action();
            }
            catch (Exception ex)
            {
                Assert.Fail($"Expected no exception, but got: {ex.GetType().Name}: {ex.Message}");
            }
        }
    }
}
