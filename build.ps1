# PowerShell script to build NetworkMonitor
# This script builds the solution and optionally creates a release package

param(
    [string]$Configuration = "Release",
    [switch]$Package,
    [switch]$Clean,
    [string]$OutputPath = "bin\Release"
)

Write-Host "NetworkMonitor Build Script" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "NetworkMonitor.sln")) {
    Write-Error "NetworkMonitor.sln not found. Please run this script from the project root directory."
    exit 1
}

# Function to check if a command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Check for required tools
$hasVSBuild = Test-Command "msbuild"
$hasDotNet = Test-Command "dotnet"

if (-not $hasVSBuild -and -not $hasDotNet) {
    Write-Error "Neither MSBuild nor .NET CLI found. Please install Visual Studio or .NET SDK."
    exit 1
}

# Determine build tool
$buildTool = if ($hasDotNet) { "dotnet" } else { "msbuild" }
Write-Host "Using build tool: $buildTool" -ForegroundColor Yellow
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow

# Clean if requested
if ($Clean) {
    Write-Host "`nCleaning solution..." -ForegroundColor Cyan
    
    if ($buildTool -eq "dotnet") {
        & dotnet clean NetworkMonitor.sln --configuration $Configuration
    } else {
        & msbuild NetworkMonitor.sln /t:Clean /p:Configuration=$Configuration
    }
    
    Write-Host "Clean completed!" -ForegroundColor Green
}

# Build the solution
Write-Host "`nBuilding solution..." -ForegroundColor Cyan

if ($buildTool -eq "dotnet") {
    $buildArgs = @(
        "build", "NetworkMonitor.sln",
        "--configuration", $Configuration,
        "--verbosity", "minimal"
    )
    
    if ($OutputPath) {
        $buildArgs += "--output", $OutputPath
    }
    
    & dotnet @buildArgs
} else {
    $buildArgs = @(
        "NetworkMonitor.sln",
        "/p:Configuration=$Configuration",
        "/verbosity:minimal"
    )
    
    if ($OutputPath) {
        $buildArgs += "/p:OutputPath=$OutputPath"
    }
    
    & msbuild @buildArgs
}

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed. Please fix compilation errors and try again."
    exit 1
}

Write-Host "Build completed successfully!" -ForegroundColor Green

# Package if requested
if ($Package) {
    Write-Host "`nCreating release package..." -ForegroundColor Cyan
    
    $packageDir = "NetworkMonitor-Release"
    $zipFile = "NetworkMonitor-Release.zip"
    
    # Remove existing package directory and zip file
    if (Test-Path $packageDir) {
        Remove-Item $packageDir -Recurse -Force
    }
    if (Test-Path $zipFile) {
        Remove-Item $zipFile -Force
    }
    
    # Create package directory
    New-Item -ItemType Directory -Path $packageDir | Out-Null
    
    # Copy main executable and dependencies
    $sourceDir = if ($OutputPath) { $OutputPath } else { "bin\$Configuration" }
    
    if (Test-Path $sourceDir) {
        Copy-Item "$sourceDir\*" $packageDir -Recurse -Force
        
        # Copy additional files
        $additionalFiles = @(
            "README.md",
            "icon.ico",
            "app.manifest"
        )
        
        foreach ($file in $additionalFiles) {
            if (Test-Path $file) {
                Copy-Item $file $packageDir -Force
            }
        }
        
        # Create a simple launcher script
        $launcherScript = @"
@echo off
echo Starting NetworkMonitor...
echo.
echo For full functionality, please run as administrator.
echo.
pause
NetworkMonitor.exe
"@
        
        $launcherScript | Out-File -FilePath "$packageDir\Start-NetworkMonitor.bat" -Encoding ASCII
        
        # Create zip package
        if (Test-Command "7z") {
            & 7z a $zipFile $packageDir\*
        } elseif (Get-Command "Compress-Archive" -ErrorAction SilentlyContinue) {
            Compress-Archive -Path "$packageDir\*" -DestinationPath $zipFile -Force
        } else {
            Write-Warning "No compression tool found. Package created in folder: $packageDir"
        }
        
        if (Test-Path $zipFile) {
            Write-Host "Package created: $zipFile" -ForegroundColor Green
        }
        
        Write-Host "Package directory: $packageDir" -ForegroundColor Green
    } else {
        Write-Error "Build output directory not found: $sourceDir"
        exit 1
    }
}

# Display build information
Write-Host "`nBuild Information:" -ForegroundColor Yellow
Write-Host "Configuration: $Configuration"
Write-Host "Build Tool: $buildTool"
Write-Host "Output Path: $OutputPath"

if (Test-Path "$OutputPath\NetworkMonitor.exe") {
    $fileInfo = Get-Item "$OutputPath\NetworkMonitor.exe"
    Write-Host "Executable Size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
    Write-Host "Build Time: $($fileInfo.LastWriteTime)"
}

Write-Host "`nBuild completed successfully!" -ForegroundColor Green
Write-Host "You can now run the application from: $OutputPath\NetworkMonitor.exe" -ForegroundColor Cyan
