# PowerShell script to create a simple ICO file for NetworkMonitor
# This creates a basic 32x32 icon with network monitoring theme

Add-Type -AssemblyName System.Drawing

# Create a 32x32 bitmap
$bitmap = New-Object System.Drawing.Bitmap(32, 32)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# Set high quality rendering
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
$graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic

# Define colors
$bgColor = [System.Drawing.Color]::FromArgb(46, 134, 171)  # Blue background
$nodeColor = [System.Drawing.Color]::FromArgb(242, 66, 54)  # Red nodes
$lineColor = [System.Drawing.Color]::White
$centerColor = [System.Drawing.Color]::White

# Fill background circle
$bgBrush = New-Object System.Drawing.SolidBrush($bgColor)
$graphics.FillEllipse($bgBrush, 2, 2, 28, 28)

# Draw network nodes
$nodeBrush = New-Object System.Drawing.SolidBrush($nodeColor)
$graphics.FillEllipse($nodeBrush, 6, 6, 4, 4)    # Top-left
$graphics.FillEllipse($nodeBrush, 22, 6, 4, 4)   # Top-right
$graphics.FillEllipse($nodeBrush, 6, 22, 4, 4)   # Bottom-left
$graphics.FillEllipse($nodeBrush, 22, 22, 4, 4)  # Bottom-right

# Draw connection lines
$linePen = New-Object System.Drawing.Pen($lineColor, 2)
$graphics.DrawLine($linePen, 8, 8, 24, 8)    # Top horizontal
$graphics.DrawLine($linePen, 8, 24, 24, 24)  # Bottom horizontal
$graphics.DrawLine($linePen, 8, 8, 8, 24)    # Left vertical
$graphics.DrawLine($linePen, 24, 8, 24, 24)  # Right vertical
$graphics.DrawLine($linePen, 8, 8, 24, 24)   # Diagonal
$graphics.DrawLine($linePen, 24, 8, 8, 24)   # Other diagonal

# Draw center monitoring circle
$centerPen = New-Object System.Drawing.Pen($centerColor, 2)
$graphics.DrawEllipse($centerPen, 12, 12, 8, 8)
$centerBrush = New-Object System.Drawing.SolidBrush($centerColor)
$graphics.FillEllipse($centerBrush, 15, 15, 2, 2)

# Clean up graphics
$graphics.Dispose()

# Save as ICO file
$iconPath = Join-Path $PSScriptRoot "icon.ico"
$icon = [System.Drawing.Icon]::FromHandle($bitmap.GetHicon())
$fileStream = [System.IO.FileStream]::new($iconPath, [System.IO.FileMode]::Create)
$icon.Save($fileStream)
$fileStream.Close()

# Clean up
$bitmap.Dispose()
$bgBrush.Dispose()
$nodeBrush.Dispose()
$linePen.Dispose()
$centerPen.Dispose()
$centerBrush.Dispose()

Write-Host "Icon created successfully at: $iconPath"
